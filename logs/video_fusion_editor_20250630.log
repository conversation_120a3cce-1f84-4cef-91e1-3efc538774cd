2025-06-30 05:50:55,806 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 05:50:55,806 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 05:50:55,807 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 05:50:55,807 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 05:50:55,807 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-30 05:50:55,807 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-30 05:50:55,807 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-30 05:50:55,807 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-30 05:50:55,807 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-30 05:50:55,807 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 05:50:55,808 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 05:50:55,808 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 05:50:55,808 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 05:50:55,808 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-30 05:50:55,808 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-30 05:50:55,808 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-30 05:50:55,808 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-30 05:50:55,808 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-30 05:50:55,808 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 05:50:55,808 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 05:50:55,808 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 05:50:55,808 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 05:50:55,808 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-30 05:50:55,808 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-30 05:50:55,808 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-30 05:50:55,808 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-30 05:50:55,809 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-30 05:50:55,809 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-30 05:50:55,809 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-30 05:50:55,809 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-30 05:50:55,809 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-30 05:50:55,809 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-30 05:50:56,415 - src.gui.main_window - INFO - 主窗口初始化完成
2025-06-30 05:50:56,416 - __main__ - INFO - 视频融合编辑器启动
2025-06-30 05:50:56,416 - __main__ - INFO - Python版本: 3.9.23 (main, Jun  5 2025, 08:23:30) 
[Clang 14.0.6 ]
2025-06-30 05:50:56,416 - __main__ - INFO - PyQt5版本: 5.15.10
2025-06-30 05:50:56,425 - src.gui.main_window - INFO - 🎉 欢迎使用视频融合编辑器！
2025-06-30 05:50:56,425 - src.gui.main_window - INFO - 💡 提示: 可以拖拽视频文件到窗口中快速加载
2025-06-30 05:50:56,426 - src.gui.main_window - INFO - 📖 使用帮助: 菜单栏 -> 帮助 -> 用户手册
2025-06-30 05:53:38,856 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'random', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': True, 'static_position_x': 0.5, 'static_position_y': 0.51, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 5, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': 'static', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': ['w', 'h', 'i', 't', 'e'], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-30 05:53:38,856 - src.fusion.fusion_engine - INFO - 使用默认帧数: 1000
2025-06-30 05:53:38,856 - src.fusion.fusion_engine - INFO - 生成了 5 个默认插入位置
2025-06-30 05:53:38,856 - src.fusion.fusion_engine - INFO - 位置 1: 帧146, 持续1帧
2025-06-30 05:53:38,856 - src.fusion.fusion_engine - INFO - 位置 2: 帧197, 持续1帧
2025-06-30 05:53:38,857 - src.fusion.fusion_engine - INFO - 位置 3: 帧350, 持续1帧
2025-06-30 05:53:38,857 - src.fusion.fusion_engine - INFO - 位置 4: 帧835, 持续1帧
2025-06-30 05:53:38,857 - src.fusion.fusion_engine - INFO - 位置 5: 帧870, 持续1帧
2025-06-30 05:53:38,857 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-30 05:53:38,857 - src.gui.main_window - INFO - 融合参数已更新
2025-06-30 05:53:38,857 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 146, 'duration': 1}, {'frame': 197, 'duration': 1}, {'frame': 350, 'duration': 1}, {'frame': 835, 'duration': 1}, {'frame': 870, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'random', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': True, 'static_position_x': 0.5, 'static_position_y': 0.51, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 5, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': 'static', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': ['w', 'h', 'i', 't', 'e'], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-30 05:53:38,858 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-30 05:53:38,858 - src.gui.main_window - INFO - 融合参数已更新
2025-06-30 05:53:38,905 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 146, 'duration': 1}, {'frame': 197, 'duration': 1}, {'frame': 350, 'duration': 1}, {'frame': 835, 'duration': 1}, {'frame': 870, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'random', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': True, 'static_position_x': 0.5, 'static_position_y': 0.52, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 5, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': 'static', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': ['w', 'h', 'i', 't', 'e'], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-30 05:53:38,906 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-30 05:53:38,906 - src.gui.main_window - INFO - 融合参数已更新
2025-06-30 05:53:38,906 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 146, 'duration': 1}, {'frame': 197, 'duration': 1}, {'frame': 350, 'duration': 1}, {'frame': 835, 'duration': 1}, {'frame': 870, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'random', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': True, 'static_position_x': 0.5, 'static_position_y': 0.52, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 5, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': 'static', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': ['w', 'h', 'i', 't', 'e'], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-30 05:53:38,906 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-30 05:53:38,907 - src.gui.main_window - INFO - 融合参数已更新
2025-06-30 05:53:38,972 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 146, 'duration': 1}, {'frame': 197, 'duration': 1}, {'frame': 350, 'duration': 1}, {'frame': 835, 'duration': 1}, {'frame': 870, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'random', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': True, 'static_position_x': 0.5, 'static_position_y': 0.53, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 5, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': 'static', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': ['w', 'h', 'i', 't', 'e'], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-30 05:53:38,972 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-30 05:53:38,973 - src.gui.main_window - INFO - 融合参数已更新
2025-06-30 05:53:38,973 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 146, 'duration': 1}, {'frame': 197, 'duration': 1}, {'frame': 350, 'duration': 1}, {'frame': 835, 'duration': 1}, {'frame': 870, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'random', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': True, 'static_position_x': 0.5, 'static_position_y': 0.53, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 5, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': 'static', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': ['w', 'h', 'i', 't', 'e'], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-30 05:53:38,973 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-30 05:53:38,973 - src.gui.main_window - INFO - 融合参数已更新
2025-06-30 05:53:39,128 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 146, 'duration': 1}, {'frame': 197, 'duration': 1}, {'frame': 350, 'duration': 1}, {'frame': 835, 'duration': 1}, {'frame': 870, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'random', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': True, 'static_position_x': 0.5, 'static_position_y': 0.54, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 5, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': 'static', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': ['w', 'h', 'i', 't', 'e'], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-30 05:53:39,129 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-30 05:53:39,129 - src.gui.main_window - INFO - 融合参数已更新
2025-06-30 05:53:39,129 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 146, 'duration': 1}, {'frame': 197, 'duration': 1}, {'frame': 350, 'duration': 1}, {'frame': 835, 'duration': 1}, {'frame': 870, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'random', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': True, 'static_position_x': 0.5, 'static_position_y': 0.54, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 5, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': 'static', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': ['w', 'h', 'i', 't', 'e'], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-30 05:53:39,129 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-30 05:53:39,130 - src.gui.main_window - INFO - 融合参数已更新
2025-06-30 05:53:39,161 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 146, 'duration': 1}, {'frame': 197, 'duration': 1}, {'frame': 350, 'duration': 1}, {'frame': 835, 'duration': 1}, {'frame': 870, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'random', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': True, 'static_position_x': 0.5, 'static_position_y': 0.55, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 5, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': 'static', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': ['w', 'h', 'i', 't', 'e'], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-30 05:53:39,162 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-30 05:53:39,162 - src.gui.main_window - INFO - 融合参数已更新
2025-06-30 05:53:39,162 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 146, 'duration': 1}, {'frame': 197, 'duration': 1}, {'frame': 350, 'duration': 1}, {'frame': 835, 'duration': 1}, {'frame': 870, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'random', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': True, 'static_position_x': 0.5, 'static_position_y': 0.55, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 5, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': 'static', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': ['w', 'h', 'i', 't', 'e'], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-30 05:53:39,162 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-30 05:53:39,163 - src.gui.main_window - INFO - 融合参数已更新
2025-06-30 05:53:39,178 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 146, 'duration': 1}, {'frame': 197, 'duration': 1}, {'frame': 350, 'duration': 1}, {'frame': 835, 'duration': 1}, {'frame': 870, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'random', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': True, 'static_position_x': 0.5, 'static_position_y': 0.56, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 5, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': 'static', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': ['w', 'h', 'i', 't', 'e'], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-30 05:53:39,178 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-30 05:53:39,178 - src.gui.main_window - INFO - 融合参数已更新
2025-06-30 05:53:39,178 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 146, 'duration': 1}, {'frame': 197, 'duration': 1}, {'frame': 350, 'duration': 1}, {'frame': 835, 'duration': 1}, {'frame': 870, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'random', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': True, 'static_position_x': 0.5, 'static_position_y': 0.56, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 5, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': 'static', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': ['w', 'h', 'i', 't', 'e'], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-30 05:53:39,179 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-30 05:53:39,179 - src.gui.main_window - INFO - 融合参数已更新
2025-06-30 05:53:39,210 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 146, 'duration': 1}, {'frame': 197, 'duration': 1}, {'frame': 350, 'duration': 1}, {'frame': 835, 'duration': 1}, {'frame': 870, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'random', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': True, 'static_position_x': 0.5, 'static_position_y': 0.57, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 5, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': 'static', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': ['w', 'h', 'i', 't', 'e'], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-30 05:53:39,211 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-30 05:53:39,211 - src.gui.main_window - INFO - 融合参数已更新
2025-06-30 05:53:39,211 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 146, 'duration': 1}, {'frame': 197, 'duration': 1}, {'frame': 350, 'duration': 1}, {'frame': 835, 'duration': 1}, {'frame': 870, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'random', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': True, 'static_position_x': 0.5, 'static_position_y': 0.57, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 5, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': 'static', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': ['w', 'h', 'i', 't', 'e'], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-30 05:53:39,211 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-30 05:53:39,212 - src.gui.main_window - INFO - 融合参数已更新
2025-06-30 05:53:39,227 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 146, 'duration': 1}, {'frame': 197, 'duration': 1}, {'frame': 350, 'duration': 1}, {'frame': 835, 'duration': 1}, {'frame': 870, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'random', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': True, 'static_position_x': 0.5, 'static_position_y': 0.58, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 5, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': 'static', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': ['w', 'h', 'i', 't', 'e'], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-30 05:53:39,227 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-30 05:53:39,227 - src.gui.main_window - INFO - 融合参数已更新
2025-06-30 05:53:39,227 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 146, 'duration': 1}, {'frame': 197, 'duration': 1}, {'frame': 350, 'duration': 1}, {'frame': 835, 'duration': 1}, {'frame': 870, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'random', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': True, 'static_position_x': 0.5, 'static_position_y': 0.58, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 5, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': 'static', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': ['w', 'h', 'i', 't', 'e'], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-30 05:53:39,228 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-30 05:53:39,228 - src.gui.main_window - INFO - 融合参数已更新
2025-06-30 05:53:39,243 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 146, 'duration': 1}, {'frame': 197, 'duration': 1}, {'frame': 350, 'duration': 1}, {'frame': 835, 'duration': 1}, {'frame': 870, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'random', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': True, 'static_position_x': 0.5, 'static_position_y': 0.59, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 5, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': 'static', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': ['w', 'h', 'i', 't', 'e'], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-30 05:53:39,244 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-30 05:53:39,244 - src.gui.main_window - INFO - 融合参数已更新
2025-06-30 05:53:39,244 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 146, 'duration': 1}, {'frame': 197, 'duration': 1}, {'frame': 350, 'duration': 1}, {'frame': 835, 'duration': 1}, {'frame': 870, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'random', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': True, 'static_position_x': 0.5, 'static_position_y': 0.59, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 5, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': 'static', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': ['w', 'h', 'i', 't', 'e'], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-30 05:53:39,244 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-30 05:53:39,244 - src.gui.main_window - INFO - 融合参数已更新
2025-06-30 05:53:39,259 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 146, 'duration': 1}, {'frame': 197, 'duration': 1}, {'frame': 350, 'duration': 1}, {'frame': 835, 'duration': 1}, {'frame': 870, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'random', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': True, 'static_position_x': 0.5, 'static_position_y': 0.6, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 5, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': 'static', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': ['w', 'h', 'i', 't', 'e'], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-30 05:53:39,259 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-30 05:53:39,259 - src.gui.main_window - INFO - 融合参数已更新
2025-06-30 05:53:39,260 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 146, 'duration': 1}, {'frame': 197, 'duration': 1}, {'frame': 350, 'duration': 1}, {'frame': 835, 'duration': 1}, {'frame': 870, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'random', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': True, 'static_position_x': 0.5, 'static_position_y': 0.6, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 5, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': 'static', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': ['w', 'h', 'i', 't', 'e'], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-30 05:53:39,260 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-30 05:53:39,260 - src.gui.main_window - INFO - 融合参数已更新
2025-06-30 05:53:39,276 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 146, 'duration': 1}, {'frame': 197, 'duration': 1}, {'frame': 350, 'duration': 1}, {'frame': 835, 'duration': 1}, {'frame': 870, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'random', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': True, 'static_position_x': 0.5, 'static_position_y': 0.61, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 5, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': 'static', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': ['w', 'h', 'i', 't', 'e'], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-30 05:53:39,276 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-30 05:53:39,277 - src.gui.main_window - INFO - 融合参数已更新
2025-06-30 05:53:39,277 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 146, 'duration': 1}, {'frame': 197, 'duration': 1}, {'frame': 350, 'duration': 1}, {'frame': 835, 'duration': 1}, {'frame': 870, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'random', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': True, 'static_position_x': 0.5, 'static_position_y': 0.61, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 5, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': 'static', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': ['w', 'h', 'i', 't', 'e'], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-30 05:53:39,277 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-30 05:53:39,277 - src.gui.main_window - INFO - 融合参数已更新
2025-06-30 05:53:39,293 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 146, 'duration': 1}, {'frame': 197, 'duration': 1}, {'frame': 350, 'duration': 1}, {'frame': 835, 'duration': 1}, {'frame': 870, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'random', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': True, 'static_position_x': 0.5, 'static_position_y': 0.62, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 5, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': 'static', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': ['w', 'h', 'i', 't', 'e'], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-30 05:53:39,293 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-30 05:53:39,293 - src.gui.main_window - INFO - 融合参数已更新
2025-06-30 05:53:39,293 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 146, 'duration': 1}, {'frame': 197, 'duration': 1}, {'frame': 350, 'duration': 1}, {'frame': 835, 'duration': 1}, {'frame': 870, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'random', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': True, 'static_position_x': 0.5, 'static_position_y': 0.62, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 5, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': 'static', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': ['w', 'h', 'i', 't', 'e'], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-30 05:53:39,294 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-30 05:53:39,294 - src.gui.main_window - INFO - 融合参数已更新
2025-06-30 05:53:39,309 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 146, 'duration': 1}, {'frame': 197, 'duration': 1}, {'frame': 350, 'duration': 1}, {'frame': 835, 'duration': 1}, {'frame': 870, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'random', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': True, 'static_position_x': 0.5, 'static_position_y': 0.63, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 5, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': 'static', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': ['w', 'h', 'i', 't', 'e'], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-30 05:53:39,309 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-30 05:53:39,309 - src.gui.main_window - INFO - 融合参数已更新
2025-06-30 05:53:39,309 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 146, 'duration': 1}, {'frame': 197, 'duration': 1}, {'frame': 350, 'duration': 1}, {'frame': 835, 'duration': 1}, {'frame': 870, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'random', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': True, 'static_position_x': 0.5, 'static_position_y': 0.63, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 5, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': 'static', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': ['w', 'h', 'i', 't', 'e'], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-30 05:53:39,309 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-30 05:53:39,310 - src.gui.main_window - INFO - 融合参数已更新
2025-06-30 05:53:39,325 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 146, 'duration': 1}, {'frame': 197, 'duration': 1}, {'frame': 350, 'duration': 1}, {'frame': 835, 'duration': 1}, {'frame': 870, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'random', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': True, 'static_position_x': 0.5, 'static_position_y': 0.64, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 5, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': 'static', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': ['w', 'h', 'i', 't', 'e'], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-30 05:53:39,326 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-30 05:53:39,326 - src.gui.main_window - INFO - 融合参数已更新
2025-06-30 05:53:39,326 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 146, 'duration': 1}, {'frame': 197, 'duration': 1}, {'frame': 350, 'duration': 1}, {'frame': 835, 'duration': 1}, {'frame': 870, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'random', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': True, 'static_position_x': 0.5, 'static_position_y': 0.64, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 5, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': 'static', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': ['w', 'h', 'i', 't', 'e'], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-30 05:53:39,327 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-30 05:53:39,327 - src.gui.main_window - INFO - 融合参数已更新
2025-06-30 05:53:39,342 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 146, 'duration': 1}, {'frame': 197, 'duration': 1}, {'frame': 350, 'duration': 1}, {'frame': 835, 'duration': 1}, {'frame': 870, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'random', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': True, 'static_position_x': 0.5, 'static_position_y': 0.65, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 5, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': 'static', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': ['w', 'h', 'i', 't', 'e'], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-30 05:53:39,343 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-30 05:53:39,343 - src.gui.main_window - INFO - 融合参数已更新
2025-06-30 05:53:39,343 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 146, 'duration': 1}, {'frame': 197, 'duration': 1}, {'frame': 350, 'duration': 1}, {'frame': 835, 'duration': 1}, {'frame': 870, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'random', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': True, 'static_position_x': 0.5, 'static_position_y': 0.65, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 5, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': 'static', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': ['w', 'h', 'i', 't', 'e'], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-30 05:53:39,343 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-30 05:53:39,343 - src.gui.main_window - INFO - 融合参数已更新
2025-06-30 05:53:39,359 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 146, 'duration': 1}, {'frame': 197, 'duration': 1}, {'frame': 350, 'duration': 1}, {'frame': 835, 'duration': 1}, {'frame': 870, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'random', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': True, 'static_position_x': 0.5, 'static_position_y': 0.66, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 5, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': 'static', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': ['w', 'h', 'i', 't', 'e'], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-30 05:53:39,360 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-30 05:53:39,360 - src.gui.main_window - INFO - 融合参数已更新
2025-06-30 05:53:39,360 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 146, 'duration': 1}, {'frame': 197, 'duration': 1}, {'frame': 350, 'duration': 1}, {'frame': 835, 'duration': 1}, {'frame': 870, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'random', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': True, 'static_position_x': 0.5, 'static_position_y': 0.66, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 5, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': 'static', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': ['w', 'h', 'i', 't', 'e'], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-30 05:53:39,360 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-30 05:53:39,360 - src.gui.main_window - INFO - 融合参数已更新
2025-06-30 05:53:39,392 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 146, 'duration': 1}, {'frame': 197, 'duration': 1}, {'frame': 350, 'duration': 1}, {'frame': 835, 'duration': 1}, {'frame': 870, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'random', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': True, 'static_position_x': 0.5, 'static_position_y': 0.67, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 5, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': 'static', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': ['w', 'h', 'i', 't', 'e'], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-30 05:53:39,393 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-30 05:53:39,393 - src.gui.main_window - INFO - 融合参数已更新
2025-06-30 05:53:39,393 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 146, 'duration': 1}, {'frame': 197, 'duration': 1}, {'frame': 350, 'duration': 1}, {'frame': 835, 'duration': 1}, {'frame': 870, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'random', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': True, 'static_position_x': 0.5, 'static_position_y': 0.67, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 5, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': 'static', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': ['w', 'h', 'i', 't', 'e'], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-30 05:53:39,393 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-30 05:53:39,393 - src.gui.main_window - INFO - 融合参数已更新
2025-06-30 05:53:39,409 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 146, 'duration': 1}, {'frame': 197, 'duration': 1}, {'frame': 350, 'duration': 1}, {'frame': 835, 'duration': 1}, {'frame': 870, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'random', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': True, 'static_position_x': 0.5, 'static_position_y': 0.68, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 5, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': 'static', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': ['w', 'h', 'i', 't', 'e'], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-30 05:53:39,409 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-30 05:53:39,409 - src.gui.main_window - INFO - 融合参数已更新
2025-06-30 05:53:39,410 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 146, 'duration': 1}, {'frame': 197, 'duration': 1}, {'frame': 350, 'duration': 1}, {'frame': 835, 'duration': 1}, {'frame': 870, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'random', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': True, 'static_position_x': 0.5, 'static_position_y': 0.68, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 5, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': 'static', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': ['w', 'h', 'i', 't', 'e'], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-30 05:53:39,410 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-30 05:53:39,410 - src.gui.main_window - INFO - 融合参数已更新
2025-06-30 05:53:39,443 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 146, 'duration': 1}, {'frame': 197, 'duration': 1}, {'frame': 350, 'duration': 1}, {'frame': 835, 'duration': 1}, {'frame': 870, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'random', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': True, 'static_position_x': 0.5, 'static_position_y': 0.69, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 5, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': 'static', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': ['w', 'h', 'i', 't', 'e'], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-30 05:53:39,443 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-30 05:53:39,443 - src.gui.main_window - INFO - 融合参数已更新
2025-06-30 05:53:39,444 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 146, 'duration': 1}, {'frame': 197, 'duration': 1}, {'frame': 350, 'duration': 1}, {'frame': 835, 'duration': 1}, {'frame': 870, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'random', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': True, 'static_position_x': 0.5, 'static_position_y': 0.69, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 5, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': 'static', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': ['w', 'h', 'i', 't', 'e'], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-30 05:53:39,444 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-30 05:53:39,444 - src.gui.main_window - INFO - 融合参数已更新
2025-06-30 05:53:39,494 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 146, 'duration': 1}, {'frame': 197, 'duration': 1}, {'frame': 350, 'duration': 1}, {'frame': 835, 'duration': 1}, {'frame': 870, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'random', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': True, 'static_position_x': 0.5, 'static_position_y': 0.7, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 5, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': 'static', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': ['w', 'h', 'i', 't', 'e'], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-30 05:53:39,495 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-30 05:53:39,495 - src.gui.main_window - INFO - 融合参数已更新
2025-06-30 05:53:39,495 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 146, 'duration': 1}, {'frame': 197, 'duration': 1}, {'frame': 350, 'duration': 1}, {'frame': 835, 'duration': 1}, {'frame': 870, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'random', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': True, 'static_position_x': 0.5, 'static_position_y': 0.7, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 5, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': 'static', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': ['w', 'h', 'i', 't', 'e'], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-30 05:53:39,495 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-30 05:53:39,495 - src.gui.main_window - INFO - 融合参数已更新
2025-06-30 05:53:39,543 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 146, 'duration': 1}, {'frame': 197, 'duration': 1}, {'frame': 350, 'duration': 1}, {'frame': 835, 'duration': 1}, {'frame': 870, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'random', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': True, 'static_position_x': 0.5, 'static_position_y': 0.71, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 5, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': 'static', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': ['w', 'h', 'i', 't', 'e'], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-30 05:53:39,543 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-30 05:53:39,543 - src.gui.main_window - INFO - 融合参数已更新
2025-06-30 05:53:39,544 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 146, 'duration': 1}, {'frame': 197, 'duration': 1}, {'frame': 350, 'duration': 1}, {'frame': 835, 'duration': 1}, {'frame': 870, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'random', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': True, 'static_position_x': 0.5, 'static_position_y': 0.71, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 5, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': 'static', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': ['w', 'h', 'i', 't', 'e'], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-30 05:53:39,544 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-30 05:53:39,544 - src.gui.main_window - INFO - 融合参数已更新
2025-06-30 05:53:39,593 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 146, 'duration': 1}, {'frame': 197, 'duration': 1}, {'frame': 350, 'duration': 1}, {'frame': 835, 'duration': 1}, {'frame': 870, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'random', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': True, 'static_position_x': 0.5, 'static_position_y': 0.72, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 5, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': 'static', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': ['w', 'h', 'i', 't', 'e'], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-30 05:53:39,593 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-30 05:53:39,593 - src.gui.main_window - INFO - 融合参数已更新
2025-06-30 05:53:39,593 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 146, 'duration': 1}, {'frame': 197, 'duration': 1}, {'frame': 350, 'duration': 1}, {'frame': 835, 'duration': 1}, {'frame': 870, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'random', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': True, 'static_position_x': 0.5, 'static_position_y': 0.72, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 5, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': 'static', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': ['w', 'h', 'i', 't', 'e'], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-30 05:53:39,593 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-30 05:53:39,593 - src.gui.main_window - INFO - 融合参数已更新
2025-06-30 05:53:39,694 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 146, 'duration': 1}, {'frame': 197, 'duration': 1}, {'frame': 350, 'duration': 1}, {'frame': 835, 'duration': 1}, {'frame': 870, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'random', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': True, 'static_position_x': 0.5, 'static_position_y': 0.73, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 5, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': 'static', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': ['w', 'h', 'i', 't', 'e'], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-30 05:53:39,694 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-30 05:53:39,694 - src.gui.main_window - INFO - 融合参数已更新
2025-06-30 05:53:39,695 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 146, 'duration': 1}, {'frame': 197, 'duration': 1}, {'frame': 350, 'duration': 1}, {'frame': 835, 'duration': 1}, {'frame': 870, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'random', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': True, 'static_position_x': 0.5, 'static_position_y': 0.73, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 5, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': 'static', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': ['w', 'h', 'i', 't', 'e'], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-30 05:53:39,695 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-30 05:53:39,695 - src.gui.main_window - INFO - 融合参数已更新
2025-06-30 05:57:10,445 - src.gui.main_window - INFO - 应用程序退出
2025-06-30 12:19:38,216 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 12:19:38,217 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 12:19:38,217 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 12:19:38,217 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 12:19:38,217 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-30 12:19:38,217 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-30 12:19:38,217 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-30 12:19:38,217 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-30 12:19:38,218 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-30 12:19:38,218 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 12:19:38,218 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 12:19:38,218 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 12:19:38,218 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 12:19:38,218 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-30 12:19:38,218 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-30 12:19:38,218 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-30 12:19:38,218 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-30 12:19:38,218 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-30 12:19:38,218 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 12:19:38,218 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 12:19:38,219 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 12:19:38,219 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 12:19:38,219 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-30 12:19:38,219 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-30 12:19:38,219 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-30 12:19:38,219 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-30 12:19:38,219 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-30 12:19:38,219 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-30 12:19:38,219 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-30 12:19:38,219 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-30 12:19:38,220 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-30 12:19:38,220 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-30 12:19:38,791 - src.gui.main_window - INFO - 主窗口初始化完成
2025-06-30 12:19:38,792 - __main__ - INFO - 视频融合编辑器启动
2025-06-30 12:19:38,792 - __main__ - INFO - Python版本: 3.9.23 (main, Jun  5 2025, 08:23:30) 
[Clang 14.0.6 ]
2025-06-30 12:19:38,792 - __main__ - INFO - PyQt5版本: 5.15.10
2025-06-30 12:19:38,802 - src.gui.main_window - INFO - 🎉 欢迎使用视频融合编辑器！
2025-06-30 12:19:38,802 - src.gui.main_window - INFO - 💡 提示: 可以拖拽视频文件到窗口中快速加载
2025-06-30 12:19:38,803 - src.gui.main_window - INFO - 📖 使用帮助: 菜单栏 -> 帮助 -> 用户手册
2025-06-30 12:22:49,164 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 12:22:49,164 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 12:22:49,164 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 12:22:49,164 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 12:22:49,164 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-30 12:22:49,164 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-30 12:22:49,165 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-30 12:22:49,165 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-30 12:22:49,165 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-30 12:22:49,165 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 12:22:49,165 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 12:22:49,165 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 12:22:49,165 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 12:22:49,165 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-30 12:22:49,165 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-30 12:22:49,165 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-30 12:22:49,165 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-30 12:22:49,165 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-30 12:22:49,166 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 12:22:49,166 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 12:22:49,166 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 12:22:49,166 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 12:22:49,166 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-30 12:22:49,166 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-30 12:22:49,166 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-30 12:22:49,166 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-30 12:22:49,166 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-30 12:22:49,166 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-30 12:22:49,166 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-30 12:22:49,166 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-30 12:22:49,166 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-30 12:22:49,167 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-30 12:23:15,192 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 12:23:15,192 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 12:23:15,193 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 12:23:15,193 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 12:23:15,193 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-30 12:23:15,193 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-30 12:23:15,193 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-30 12:23:15,193 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-30 12:23:15,193 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-30 12:23:15,194 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 12:23:15,194 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 12:23:15,194 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 12:23:15,194 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 12:23:15,194 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-30 12:23:15,194 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-30 12:23:15,194 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-30 12:23:15,194 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-30 12:23:15,194 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-30 12:23:15,194 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 12:23:15,194 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 12:23:15,194 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 12:23:15,194 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 12:23:15,194 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-30 12:23:15,195 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-30 12:23:15,195 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-30 12:23:15,195 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-30 12:23:15,195 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-30 12:23:15,195 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-30 12:23:15,195 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-30 12:23:15,195 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-30 12:23:15,195 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-30 12:23:15,195 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-30 12:23:15,589 - src.gui.main_window - INFO - 主窗口初始化完成
2025-06-30 12:23:15,589 - __main__ - INFO - 视频融合编辑器启动
2025-06-30 12:23:15,589 - __main__ - INFO - Python版本: 3.9.23 (main, Jun  5 2025, 08:23:30) 
[Clang 14.0.6 ]
2025-06-30 12:23:15,589 - __main__ - INFO - PyQt5版本: 5.15.10
2025-06-30 12:23:15,593 - src.gui.main_window - INFO - 🎉 欢迎使用视频融合编辑器！
2025-06-30 12:23:15,593 - src.gui.main_window - INFO - 💡 提示: 可以拖拽视频文件到窗口中快速加载
2025-06-30 12:23:15,593 - src.gui.main_window - INFO - 📖 使用帮助: 菜单栏 -> 帮助 -> 用户手册
2025-06-30 12:24:34,054 - src.gui.main_window - INFO - 正在加载A视频: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-30 12:24:34,109 - src.video.video_loader - INFO - 视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-30 12:24:34,109 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-30 12:24:34,120 - src.video.video_loader - INFO - 视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-30 12:24:34,120 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-30 12:24:34,121 - src.fusion.fusion_engine - INFO - A视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-30 12:24:34,121 - src.gui.main_window - INFO - A视频信息: 640x360, 帧率: 24.00 FPS, 时长: 11.50秒
2025-06-30 12:24:34,121 - src.gui.main_window - INFO - A视频融合引擎加载成功: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-30 12:24:34,121 - src.gui.video_player - WARNING - 帧号超出范围: 0
2025-06-30 12:24:34,121 - src.gui.main_window - INFO - A视频播放状态: 暂停
2025-06-30 12:24:34,133 - src.video.video_loader - INFO - 视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-30 12:24:34,133 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-30 12:24:34,158 - src.gui.video_player - INFO - 视频播放器加载视频成功: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-30 12:24:34,158 - src.gui.main_window - INFO - A视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-30 12:24:35,952 - src.gui.video_player - INFO - 播放状态变更: 播放
2025-06-30 12:24:35,953 - src.gui.main_window - INFO - A视频播放状态: 播放
2025-06-30 12:24:35,960 - src.gui.main_window - INFO - A视频播放位置: 第0帧
2025-06-30 12:24:36,010 - src.gui.main_window - INFO - A视频播放位置: 第1帧
2025-06-30 12:24:36,060 - src.gui.main_window - INFO - A视频播放位置: 第2帧
2025-06-30 12:24:36,109 - src.gui.main_window - INFO - A视频播放位置: 第3帧
2025-06-30 12:24:36,157 - src.gui.main_window - INFO - A视频播放位置: 第4帧
2025-06-30 12:24:36,206 - src.gui.main_window - INFO - A视频播放位置: 第5帧
2025-06-30 12:24:36,254 - src.gui.main_window - INFO - A视频播放位置: 第6帧
2025-06-30 12:24:36,304 - src.gui.main_window - INFO - A视频播放位置: 第7帧
2025-06-30 12:24:36,355 - src.gui.main_window - INFO - A视频播放位置: 第8帧
2025-06-30 12:24:36,409 - src.gui.main_window - INFO - A视频播放位置: 第9帧
2025-06-30 12:24:36,460 - src.gui.main_window - INFO - A视频播放位置: 第10帧
2025-06-30 12:24:36,511 - src.gui.main_window - INFO - A视频播放位置: 第11帧
2025-06-30 12:24:36,563 - src.gui.main_window - INFO - A视频播放位置: 第12帧
2025-06-30 12:24:36,618 - src.gui.main_window - INFO - A视频播放位置: 第13帧
2025-06-30 12:24:36,673 - src.gui.main_window - INFO - A视频播放位置: 第14帧
2025-06-30 12:24:36,723 - src.gui.main_window - INFO - A视频播放位置: 第15帧
2025-06-30 12:24:36,776 - src.gui.main_window - INFO - A视频播放位置: 第16帧
2025-06-30 12:24:36,830 - src.gui.main_window - INFO - A视频播放位置: 第17帧
2025-06-30 12:24:36,885 - src.gui.main_window - INFO - A视频播放位置: 第18帧
2025-06-30 12:24:36,936 - src.gui.main_window - INFO - A视频播放位置: 第19帧
2025-06-30 12:24:36,987 - src.gui.main_window - INFO - A视频播放位置: 第20帧
2025-06-30 12:24:37,042 - src.gui.main_window - INFO - A视频播放位置: 第21帧
2025-06-30 12:24:37,095 - src.gui.main_window - INFO - A视频播放位置: 第22帧
2025-06-30 12:24:37,154 - src.gui.main_window - INFO - A视频播放位置: 第23帧
2025-06-30 12:24:37,207 - src.gui.main_window - INFO - A视频播放位置: 第24帧
2025-06-30 12:24:37,263 - src.gui.main_window - INFO - A视频播放位置: 第25帧
2025-06-30 12:24:37,318 - src.gui.main_window - INFO - A视频播放位置: 第26帧
2025-06-30 12:24:37,372 - src.gui.main_window - INFO - A视频播放位置: 第27帧
2025-06-30 12:24:37,424 - src.gui.main_window - INFO - A视频播放位置: 第28帧
2025-06-30 12:24:37,482 - src.gui.main_window - INFO - A视频播放位置: 第29帧
2025-06-30 12:24:37,540 - src.gui.main_window - INFO - A视频播放位置: 第30帧
2025-06-30 12:24:37,597 - src.gui.main_window - INFO - A视频播放位置: 第31帧
2025-06-30 12:24:37,652 - src.gui.main_window - INFO - A视频播放位置: 第32帧
2025-06-30 12:24:37,710 - src.gui.main_window - INFO - A视频播放位置: 第33帧
2025-06-30 12:24:37,767 - src.gui.main_window - INFO - A视频播放位置: 第34帧
2025-06-30 12:24:37,824 - src.gui.main_window - INFO - A视频播放位置: 第35帧
2025-06-30 12:24:37,878 - src.gui.main_window - INFO - A视频播放位置: 第36帧
2025-06-30 12:24:37,935 - src.gui.main_window - INFO - A视频播放位置: 第37帧
2025-06-30 12:24:37,993 - src.gui.main_window - INFO - A视频播放位置: 第38帧
2025-06-30 12:24:38,052 - src.gui.main_window - INFO - A视频播放位置: 第39帧
2025-06-30 12:24:38,108 - src.gui.main_window - INFO - A视频播放位置: 第40帧
2025-06-30 12:24:38,163 - src.gui.main_window - INFO - A视频播放位置: 第41帧
2025-06-30 12:24:38,219 - src.gui.main_window - INFO - A视频播放位置: 第42帧
2025-06-30 12:24:38,274 - src.gui.main_window - INFO - A视频播放位置: 第43帧
2025-06-30 12:24:38,331 - src.gui.main_window - INFO - A视频播放位置: 第44帧
2025-06-30 12:24:38,387 - src.gui.main_window - INFO - A视频播放位置: 第45帧
2025-06-30 12:24:38,447 - src.gui.main_window - INFO - A视频播放位置: 第46帧
2025-06-30 12:24:38,506 - src.gui.main_window - INFO - A视频播放位置: 第47帧
2025-06-30 12:24:38,567 - src.gui.main_window - INFO - A视频播放位置: 第48帧
2025-06-30 12:24:38,627 - src.gui.main_window - INFO - A视频播放位置: 第49帧
2025-06-30 12:24:38,683 - src.gui.main_window - INFO - A视频播放位置: 第50帧
2025-06-30 12:24:38,747 - src.gui.main_window - INFO - A视频播放位置: 第51帧
2025-06-30 12:24:38,805 - src.gui.main_window - INFO - A视频播放位置: 第52帧
2025-06-30 12:24:38,864 - src.gui.main_window - INFO - A视频播放位置: 第53帧
2025-06-30 12:24:38,926 - src.gui.main_window - INFO - A视频播放位置: 第54帧
2025-06-30 12:24:38,988 - src.gui.main_window - INFO - A视频播放位置: 第55帧
2025-06-30 12:24:39,053 - src.gui.main_window - INFO - A视频播放位置: 第56帧
2025-06-30 12:24:39,123 - src.gui.main_window - INFO - A视频播放位置: 第57帧
2025-06-30 12:24:39,188 - src.gui.main_window - INFO - A视频播放位置: 第58帧
2025-06-30 12:24:39,250 - src.gui.main_window - INFO - A视频播放位置: 第59帧
2025-06-30 12:24:39,331 - src.gui.main_window - INFO - A视频播放位置: 第60帧
2025-06-30 12:24:39,396 - src.gui.main_window - INFO - A视频播放位置: 第61帧
2025-06-30 12:24:39,459 - src.gui.main_window - INFO - A视频播放位置: 第62帧
2025-06-30 12:24:39,522 - src.gui.main_window - INFO - A视频播放位置: 第63帧
2025-06-30 12:24:39,581 - src.gui.main_window - INFO - A视频播放位置: 第64帧
2025-06-30 12:24:39,638 - src.gui.main_window - INFO - A视频播放位置: 第65帧
2025-06-30 12:24:39,699 - src.gui.main_window - INFO - A视频播放位置: 第66帧
2025-06-30 12:24:39,765 - src.gui.main_window - INFO - A视频播放位置: 第67帧
2025-06-30 12:24:39,831 - src.gui.main_window - INFO - A视频播放位置: 第68帧
2025-06-30 12:24:39,895 - src.gui.main_window - INFO - A视频播放位置: 第69帧
2025-06-30 12:24:39,958 - src.gui.main_window - INFO - A视频播放位置: 第70帧
2025-06-30 12:24:40,022 - src.gui.main_window - INFO - A视频播放位置: 第71帧
2025-06-30 12:24:40,089 - src.gui.main_window - INFO - A视频播放位置: 第72帧
2025-06-30 12:24:40,153 - src.gui.main_window - INFO - A视频播放位置: 第73帧
2025-06-30 12:24:40,220 - src.gui.main_window - INFO - A视频播放位置: 第74帧
2025-06-30 12:24:40,290 - src.gui.main_window - INFO - A视频播放位置: 第75帧
2025-06-30 12:24:40,360 - src.gui.main_window - INFO - A视频播放位置: 第76帧
2025-06-30 12:24:40,428 - src.gui.main_window - INFO - A视频播放位置: 第77帧
2025-06-30 12:24:40,498 - src.gui.main_window - INFO - A视频播放位置: 第78帧
2025-06-30 12:24:40,567 - src.gui.main_window - INFO - A视频播放位置: 第79帧
2025-06-30 12:24:40,636 - src.gui.main_window - INFO - A视频播放位置: 第80帧
2025-06-30 12:24:40,703 - src.gui.main_window - INFO - A视频播放位置: 第81帧
2025-06-30 12:24:40,775 - src.gui.main_window - INFO - A视频播放位置: 第82帧
2025-06-30 12:24:40,846 - src.gui.main_window - INFO - A视频播放位置: 第83帧
2025-06-30 12:24:40,917 - src.gui.main_window - INFO - A视频播放位置: 第84帧
2025-06-30 12:24:40,985 - src.gui.main_window - INFO - A视频播放位置: 第85帧
2025-06-30 12:24:41,051 - src.gui.main_window - INFO - A视频播放位置: 第86帧
2025-06-30 12:24:41,124 - src.gui.main_window - INFO - A视频播放位置: 第87帧
2025-06-30 12:24:41,193 - src.gui.main_window - INFO - A视频播放位置: 第88帧
2025-06-30 12:24:41,248 - src.gui.main_window - INFO - A视频播放位置: 第89帧
2025-06-30 12:24:41,302 - src.gui.main_window - INFO - A视频播放位置: 第90帧
2025-06-30 12:24:41,354 - src.gui.main_window - INFO - A视频播放位置: 第91帧
2025-06-30 12:24:41,413 - src.gui.main_window - INFO - A视频播放位置: 第92帧
2025-06-30 12:24:41,467 - src.gui.main_window - INFO - A视频播放位置: 第93帧
2025-06-30 12:24:41,525 - src.gui.main_window - INFO - A视频播放位置: 第94帧
2025-06-30 12:24:41,585 - src.gui.main_window - INFO - A视频播放位置: 第95帧
2025-06-30 12:24:41,640 - src.gui.main_window - INFO - A视频播放位置: 第96帧
2025-06-30 12:24:41,700 - src.gui.main_window - INFO - A视频播放位置: 第97帧
2025-06-30 12:24:41,758 - src.gui.main_window - INFO - A视频播放位置: 第98帧
2025-06-30 12:24:41,817 - src.gui.main_window - INFO - A视频播放位置: 第99帧
2025-06-30 12:24:41,874 - src.gui.main_window - INFO - A视频播放位置: 第100帧
2025-06-30 12:24:41,931 - src.gui.main_window - INFO - A视频播放位置: 第101帧
2025-06-30 12:24:41,987 - src.gui.main_window - INFO - A视频播放位置: 第102帧
2025-06-30 12:24:42,045 - src.gui.main_window - INFO - A视频播放位置: 第103帧
2025-06-30 12:24:42,106 - src.gui.main_window - INFO - A视频播放位置: 第104帧
2025-06-30 12:24:42,169 - src.gui.main_window - INFO - A视频播放位置: 第105帧
2025-06-30 12:24:42,231 - src.gui.main_window - INFO - A视频播放位置: 第106帧
2025-06-30 12:24:42,293 - src.gui.main_window - INFO - A视频播放位置: 第107帧
2025-06-30 12:24:42,366 - src.gui.main_window - INFO - A视频播放位置: 第108帧
2025-06-30 12:24:42,421 - src.gui.main_window - INFO - A视频播放位置: 第109帧
2025-06-30 12:24:42,478 - src.gui.main_window - INFO - A视频播放位置: 第110帧
2025-06-30 12:24:42,533 - src.gui.main_window - INFO - A视频播放位置: 第111帧
2025-06-30 12:24:42,594 - src.gui.main_window - INFO - A视频播放位置: 第112帧
2025-06-30 12:24:42,657 - src.gui.main_window - INFO - A视频播放位置: 第113帧
2025-06-30 12:24:42,720 - src.gui.main_window - INFO - A视频播放位置: 第114帧
2025-06-30 12:24:42,779 - src.gui.main_window - INFO - A视频播放位置: 第115帧
2025-06-30 12:24:42,840 - src.gui.main_window - INFO - A视频播放位置: 第116帧
2025-06-30 12:24:42,902 - src.gui.main_window - INFO - A视频播放位置: 第117帧
2025-06-30 12:24:42,965 - src.gui.main_window - INFO - A视频播放位置: 第118帧
2025-06-30 12:24:43,025 - src.gui.main_window - INFO - A视频播放位置: 第119帧
2025-06-30 12:24:43,090 - src.gui.main_window - INFO - A视频播放位置: 第120帧
2025-06-30 12:24:43,155 - src.gui.main_window - INFO - A视频播放位置: 第121帧
2025-06-30 12:24:43,220 - src.gui.main_window - INFO - A视频播放位置: 第122帧
2025-06-30 12:24:43,282 - src.gui.main_window - INFO - A视频播放位置: 第123帧
2025-06-30 12:24:43,342 - src.gui.main_window - INFO - A视频播放位置: 第124帧
2025-06-30 12:24:43,406 - src.gui.main_window - INFO - A视频播放位置: 第125帧
2025-06-30 12:24:43,470 - src.gui.main_window - INFO - A视频播放位置: 第126帧
2025-06-30 12:24:43,536 - src.gui.main_window - INFO - A视频播放位置: 第127帧
2025-06-30 12:24:43,598 - src.gui.main_window - INFO - A视频播放位置: 第128帧
2025-06-30 12:24:43,663 - src.gui.main_window - INFO - A视频播放位置: 第129帧
2025-06-30 12:24:43,726 - src.gui.main_window - INFO - A视频播放位置: 第130帧
2025-06-30 12:24:43,791 - src.gui.main_window - INFO - A视频播放位置: 第131帧
2025-06-30 12:24:43,854 - src.gui.main_window - INFO - A视频播放位置: 第132帧
2025-06-30 12:24:43,918 - src.gui.main_window - INFO - A视频播放位置: 第133帧
2025-06-30 12:24:43,980 - src.gui.main_window - INFO - A视频播放位置: 第134帧
2025-06-30 12:24:44,042 - src.gui.main_window - INFO - A视频播放位置: 第135帧
2025-06-30 12:24:44,107 - src.gui.main_window - INFO - A视频播放位置: 第136帧
2025-06-30 12:24:44,174 - src.gui.main_window - INFO - A视频播放位置: 第137帧
2025-06-30 12:24:44,237 - src.gui.main_window - INFO - A视频播放位置: 第138帧
2025-06-30 12:24:44,295 - src.gui.main_window - INFO - A视频播放位置: 第139帧
2025-06-30 12:24:44,358 - src.gui.main_window - INFO - A视频播放位置: 第140帧
2025-06-30 12:24:44,424 - src.gui.main_window - INFO - A视频播放位置: 第141帧
2025-06-30 12:24:44,489 - src.gui.main_window - INFO - A视频播放位置: 第142帧
2025-06-30 12:24:44,556 - src.gui.main_window - INFO - A视频播放位置: 第143帧
2025-06-30 12:24:44,622 - src.gui.main_window - INFO - A视频播放位置: 第144帧
2025-06-30 12:24:44,689 - src.gui.main_window - INFO - A视频播放位置: 第145帧
2025-06-30 12:24:44,758 - src.gui.main_window - INFO - A视频播放位置: 第146帧
2025-06-30 12:24:44,825 - src.gui.main_window - INFO - A视频播放位置: 第147帧
2025-06-30 12:24:44,897 - src.gui.main_window - INFO - A视频播放位置: 第148帧
2025-06-30 12:24:44,963 - src.gui.main_window - INFO - A视频播放位置: 第149帧
2025-06-30 12:24:45,037 - src.gui.main_window - INFO - A视频播放位置: 第150帧
2025-06-30 12:24:45,104 - src.gui.main_window - INFO - A视频播放位置: 第151帧
2025-06-30 12:24:45,174 - src.gui.main_window - INFO - A视频播放位置: 第152帧
2025-06-30 12:24:45,243 - src.gui.main_window - INFO - A视频播放位置: 第153帧
2025-06-30 12:24:45,307 - src.gui.main_window - INFO - A视频播放位置: 第154帧
2025-06-30 12:24:45,379 - src.gui.main_window - INFO - A视频播放位置: 第155帧
2025-06-30 12:24:45,447 - src.gui.main_window - INFO - A视频播放位置: 第156帧
2025-06-30 12:24:45,516 - src.gui.main_window - INFO - A视频播放位置: 第157帧
2025-06-30 12:24:45,589 - src.gui.main_window - INFO - A视频播放位置: 第158帧
2025-06-30 12:24:45,661 - src.gui.main_window - INFO - A视频播放位置: 第159帧
2025-06-30 12:24:45,733 - src.gui.main_window - INFO - A视频播放位置: 第160帧
2025-06-30 12:24:45,791 - src.gui.main_window - INFO - A视频播放位置: 第161帧
2025-06-30 12:24:45,845 - src.gui.main_window - INFO - A视频播放位置: 第162帧
2025-06-30 12:24:45,899 - src.gui.main_window - INFO - A视频播放位置: 第163帧
2025-06-30 12:24:45,957 - src.gui.main_window - INFO - A视频播放位置: 第164帧
2025-06-30 12:24:46,014 - src.gui.main_window - INFO - A视频播放位置: 第165帧
2025-06-30 12:24:46,071 - src.gui.main_window - INFO - A视频播放位置: 第166帧
2025-06-30 12:24:46,129 - src.gui.main_window - INFO - A视频播放位置: 第167帧
2025-06-30 12:24:46,184 - src.gui.main_window - INFO - A视频播放位置: 第168帧
2025-06-30 12:24:46,244 - src.gui.main_window - INFO - A视频播放位置: 第169帧
2025-06-30 12:24:46,297 - src.gui.main_window - INFO - A视频播放位置: 第170帧
2025-06-30 12:24:46,351 - src.gui.main_window - INFO - A视频播放位置: 第171帧
2025-06-30 12:24:46,410 - src.gui.main_window - INFO - A视频播放位置: 第172帧
2025-06-30 12:24:46,466 - src.gui.main_window - INFO - A视频播放位置: 第173帧
2025-06-30 12:24:46,523 - src.gui.main_window - INFO - A视频播放位置: 第174帧
2025-06-30 12:24:46,583 - src.gui.main_window - INFO - A视频播放位置: 第175帧
2025-06-30 12:24:46,644 - src.gui.main_window - INFO - A视频播放位置: 第176帧
2025-06-30 12:24:46,703 - src.gui.main_window - INFO - A视频播放位置: 第177帧
2025-06-30 12:24:46,763 - src.gui.main_window - INFO - A视频播放位置: 第178帧
2025-06-30 12:24:46,820 - src.gui.main_window - INFO - A视频播放位置: 第179帧
2025-06-30 12:24:46,882 - src.gui.main_window - INFO - A视频播放位置: 第180帧
2025-06-30 12:24:46,942 - src.gui.main_window - INFO - A视频播放位置: 第181帧
2025-06-30 12:24:47,004 - src.gui.main_window - INFO - A视频播放位置: 第182帧
2025-06-30 12:24:47,066 - src.gui.main_window - INFO - A视频播放位置: 第183帧
2025-06-30 12:24:47,127 - src.gui.main_window - INFO - A视频播放位置: 第184帧
2025-06-30 12:24:47,189 - src.gui.main_window - INFO - A视频播放位置: 第185帧
2025-06-30 12:24:47,252 - src.gui.main_window - INFO - A视频播放位置: 第186帧
2025-06-30 12:24:47,310 - src.gui.main_window - INFO - A视频播放位置: 第187帧
2025-06-30 12:24:47,370 - src.gui.main_window - INFO - A视频播放位置: 第188帧
2025-06-30 12:24:47,433 - src.gui.main_window - INFO - A视频播放位置: 第189帧
2025-06-30 12:24:47,493 - src.gui.main_window - INFO - A视频播放位置: 第190帧
2025-06-30 12:24:47,556 - src.gui.main_window - INFO - A视频播放位置: 第191帧
2025-06-30 12:24:47,617 - src.gui.main_window - INFO - A视频播放位置: 第192帧
2025-06-30 12:24:47,682 - src.gui.main_window - INFO - A视频播放位置: 第193帧
2025-06-30 12:24:47,760 - src.gui.main_window - INFO - A视频播放位置: 第194帧
2025-06-30 12:24:47,823 - src.gui.main_window - INFO - A视频播放位置: 第195帧
2025-06-30 12:24:47,881 - src.gui.main_window - INFO - A视频播放位置: 第196帧
2025-06-30 12:24:47,941 - src.gui.main_window - INFO - A视频播放位置: 第197帧
2025-06-30 12:24:48,005 - src.gui.main_window - INFO - A视频播放位置: 第198帧
2025-06-30 12:24:48,068 - src.gui.main_window - INFO - A视频播放位置: 第199帧
2025-06-30 12:24:48,129 - src.gui.main_window - INFO - A视频播放位置: 第200帧
2025-06-30 12:24:48,191 - src.gui.main_window - INFO - A视频播放位置: 第201帧
2025-06-30 12:24:48,257 - src.gui.main_window - INFO - A视频播放位置: 第202帧
2025-06-30 12:24:48,317 - src.gui.main_window - INFO - A视频播放位置: 第203帧
2025-06-30 12:24:48,380 - src.gui.main_window - INFO - A视频播放位置: 第204帧
2025-06-30 12:24:48,445 - src.gui.main_window - INFO - A视频播放位置: 第205帧
2025-06-30 12:24:48,513 - src.gui.main_window - INFO - A视频播放位置: 第206帧
2025-06-30 12:24:48,576 - src.gui.main_window - INFO - A视频播放位置: 第207帧
2025-06-30 12:24:48,643 - src.gui.main_window - INFO - A视频播放位置: 第208帧
2025-06-30 12:24:48,712 - src.gui.main_window - INFO - A视频播放位置: 第209帧
2025-06-30 12:24:48,777 - src.gui.main_window - INFO - A视频播放位置: 第210帧
2025-06-30 12:24:48,843 - src.gui.main_window - INFO - A视频播放位置: 第211帧
2025-06-30 12:24:48,912 - src.gui.main_window - INFO - A视频播放位置: 第212帧
2025-06-30 12:24:48,977 - src.gui.main_window - INFO - A视频播放位置: 第213帧
2025-06-30 12:24:49,044 - src.gui.main_window - INFO - A视频播放位置: 第214帧
2025-06-30 12:24:49,110 - src.gui.main_window - INFO - A视频播放位置: 第215帧
2025-06-30 12:24:49,179 - src.gui.main_window - INFO - A视频播放位置: 第216帧
2025-06-30 12:24:49,246 - src.gui.main_window - INFO - A视频播放位置: 第217帧
2025-06-30 12:24:49,313 - src.gui.main_window - INFO - A视频播放位置: 第218帧
2025-06-30 12:24:49,384 - src.gui.main_window - INFO - A视频播放位置: 第219帧
2025-06-30 12:24:49,454 - src.gui.main_window - INFO - A视频播放位置: 第220帧
2025-06-30 12:24:49,521 - src.gui.main_window - INFO - A视频播放位置: 第221帧
2025-06-30 12:24:49,587 - src.gui.main_window - INFO - A视频播放位置: 第222帧
2025-06-30 12:24:49,659 - src.gui.main_window - INFO - A视频播放位置: 第223帧
2025-06-30 12:24:49,728 - src.gui.main_window - INFO - A视频播放位置: 第224帧
2025-06-30 12:24:49,796 - src.gui.main_window - INFO - A视频播放位置: 第225帧
2025-06-30 12:24:49,864 - src.gui.main_window - INFO - A视频播放位置: 第226帧
2025-06-30 12:24:49,932 - src.gui.main_window - INFO - A视频播放位置: 第227帧
2025-06-30 12:24:50,001 - src.gui.main_window - INFO - A视频播放位置: 第228帧
2025-06-30 12:24:50,071 - src.gui.main_window - INFO - A视频播放位置: 第229帧
2025-06-30 12:24:50,139 - src.gui.main_window - INFO - A视频播放位置: 第230帧
2025-06-30 12:24:50,213 - src.gui.main_window - INFO - A视频播放位置: 第231帧
2025-06-30 12:24:50,285 - src.gui.main_window - INFO - A视频播放位置: 第232帧
2025-06-30 12:24:50,347 - src.gui.main_window - INFO - A视频播放位置: 第233帧
2025-06-30 12:24:50,405 - src.gui.main_window - INFO - A视频播放位置: 第234帧
2025-06-30 12:24:50,464 - src.gui.main_window - INFO - A视频播放位置: 第235帧
2025-06-30 12:24:50,522 - src.gui.main_window - INFO - A视频播放位置: 第236帧
2025-06-30 12:24:50,578 - src.gui.main_window - INFO - A视频播放位置: 第237帧
2025-06-30 12:24:50,634 - src.gui.main_window - INFO - A视频播放位置: 第238帧
2025-06-30 12:24:50,692 - src.gui.main_window - INFO - A视频播放位置: 第239帧
2025-06-30 12:24:50,762 - src.gui.main_window - INFO - A视频播放位置: 第240帧
2025-06-30 12:24:50,818 - src.gui.main_window - INFO - A视频播放位置: 第241帧
2025-06-30 12:24:50,875 - src.gui.main_window - INFO - A视频播放位置: 第242帧
2025-06-30 12:24:50,931 - src.gui.main_window - INFO - A视频播放位置: 第243帧
2025-06-30 12:24:50,991 - src.gui.main_window - INFO - A视频播放位置: 第244帧
2025-06-30 12:24:51,047 - src.gui.main_window - INFO - A视频播放位置: 第245帧
2025-06-30 12:24:51,102 - src.gui.main_window - INFO - A视频播放位置: 第246帧
2025-06-30 12:24:51,159 - src.gui.main_window - INFO - A视频播放位置: 第247帧
2025-06-30 12:24:51,218 - src.gui.main_window - INFO - A视频播放位置: 第248帧
2025-06-30 12:24:51,272 - src.gui.main_window - INFO - A视频播放位置: 第249帧
2025-06-30 12:24:51,328 - src.gui.main_window - INFO - A视频播放位置: 第250帧
2025-06-30 12:24:51,386 - src.gui.main_window - INFO - A视频播放位置: 第251帧
2025-06-30 12:24:51,441 - src.gui.main_window - INFO - A视频播放位置: 第252帧
2025-06-30 12:24:51,501 - src.gui.main_window - INFO - A视频播放位置: 第253帧
2025-06-30 12:24:51,557 - src.gui.main_window - INFO - A视频播放位置: 第254帧
2025-06-30 12:24:51,614 - src.gui.main_window - INFO - A视频播放位置: 第255帧
2025-06-30 12:24:51,677 - src.gui.main_window - INFO - A视频播放位置: 第256帧
2025-06-30 12:24:51,738 - src.gui.main_window - INFO - A视频播放位置: 第257帧
2025-06-30 12:24:51,798 - src.gui.main_window - INFO - A视频播放位置: 第258帧
2025-06-30 12:24:51,861 - src.gui.main_window - INFO - A视频播放位置: 第259帧
2025-06-30 12:24:51,919 - src.gui.main_window - INFO - A视频播放位置: 第260帧
2025-06-30 12:24:51,981 - src.gui.main_window - INFO - A视频播放位置: 第261帧
2025-06-30 12:24:52,045 - src.gui.main_window - INFO - A视频播放位置: 第262帧
2025-06-30 12:24:52,105 - src.gui.main_window - INFO - A视频播放位置: 第263帧
2025-06-30 12:24:52,168 - src.gui.main_window - INFO - A视频播放位置: 第264帧
2025-06-30 12:24:52,227 - src.gui.main_window - INFO - A视频播放位置: 第265帧
2025-06-30 12:24:52,288 - src.gui.main_window - INFO - A视频播放位置: 第266帧
2025-06-30 12:24:52,351 - src.gui.main_window - INFO - A视频播放位置: 第267帧
2025-06-30 12:24:52,411 - src.gui.main_window - INFO - A视频播放位置: 第268帧
2025-06-30 12:24:52,473 - src.gui.main_window - INFO - A视频播放位置: 第269帧
2025-06-30 12:24:52,536 - src.gui.main_window - INFO - A视频播放位置: 第270帧
2025-06-30 12:24:52,596 - src.gui.main_window - INFO - A视频播放位置: 第271帧
2025-06-30 12:24:52,656 - src.gui.main_window - INFO - A视频播放位置: 第272帧
2025-06-30 12:24:52,716 - src.gui.main_window - INFO - A视频播放位置: 第273帧
2025-06-30 12:24:52,780 - src.gui.main_window - INFO - A视频播放位置: 第274帧
2025-06-30 12:24:52,838 - src.gui.main_window - INFO - A视频播放位置: 第275帧
2025-06-30 12:24:52,839 - src.gui.video_player - INFO - 播放状态变更: 暂停
2025-06-30 12:24:52,845 - src.gui.main_window - INFO - A视频播放状态: 暂停
2025-06-30 12:24:52,845 - src.gui.video_player - INFO - 视频播放完成
2025-06-30 12:25:15,766 - src.gui.main_window - INFO - 应用程序退出
2025-06-30 12:26:01,084 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 12:26:01,084 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 12:26:01,084 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 12:26:01,084 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 12:26:01,084 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-30 12:26:01,084 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-30 12:26:01,084 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-30 12:26:01,084 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-30 12:26:01,085 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-30 12:26:01,085 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 12:26:01,085 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 12:26:01,085 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 12:26:01,085 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 12:26:01,085 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-30 12:26:01,085 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-30 12:26:01,085 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-30 12:26:01,085 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-30 12:26:01,085 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-30 12:26:01,085 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 12:26:01,085 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 12:26:01,085 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 12:26:01,086 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 12:26:01,086 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-30 12:26:01,086 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-30 12:26:01,086 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-30 12:26:01,086 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-30 12:26:01,086 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-30 12:26:01,086 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-30 12:26:01,086 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-30 12:26:01,086 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-30 12:26:01,086 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-30 12:26:01,086 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-30 12:26:01,481 - src.gui.main_window - INFO - 主窗口初始化完成
2025-06-30 12:26:01,482 - __main__ - INFO - 视频融合编辑器启动
2025-06-30 12:26:01,482 - __main__ - INFO - Python版本: 3.9.23 (main, Jun  5 2025, 08:23:30) 
[Clang 14.0.6 ]
2025-06-30 12:26:01,482 - __main__ - INFO - PyQt5版本: 5.15.10
2025-06-30 12:26:01,491 - src.gui.main_window - INFO - 🎉 欢迎使用视频融合编辑器！
2025-06-30 12:26:01,491 - src.gui.main_window - INFO - 💡 提示: 可以拖拽视频文件到窗口中快速加载
2025-06-30 12:26:01,491 - src.gui.main_window - INFO - 📖 使用帮助: 菜单栏 -> 帮助 -> 用户手册
2025-06-30 12:29:35,233 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 12:29:35,234 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 12:29:35,234 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 12:29:35,234 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 12:29:35,234 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-30 12:29:35,234 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-30 12:29:35,234 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-30 12:29:35,234 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-30 12:29:35,234 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-30 12:29:35,235 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 12:29:35,235 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 12:29:35,235 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 12:29:35,235 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 12:29:35,235 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-30 12:29:35,235 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-30 12:29:35,235 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-30 12:29:35,235 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-30 12:29:35,235 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-30 12:29:35,236 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 12:29:35,236 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 12:29:35,236 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 12:29:35,236 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 12:29:35,236 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-30 12:29:35,236 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-30 12:29:35,236 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-30 12:29:35,236 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-30 12:29:35,236 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-30 12:29:35,236 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-30 12:29:35,236 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-30 12:29:35,237 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-30 12:29:35,237 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-30 12:29:35,237 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-30 12:29:35,674 - src.gui.main_window - INFO - 主窗口初始化完成
2025-06-30 12:29:35,675 - __main__ - INFO - 视频融合编辑器启动
2025-06-30 12:29:35,675 - __main__ - INFO - Python版本: 3.9.23 (main, Jun  5 2025, 08:23:30) 
[Clang 14.0.6 ]
2025-06-30 12:29:35,675 - __main__ - INFO - PyQt5版本: 5.15.10
2025-06-30 12:29:35,684 - src.gui.main_window - INFO - 🎉 欢迎使用视频融合编辑器！
2025-06-30 12:29:35,684 - src.gui.main_window - INFO - 💡 提示: 可以拖拽视频文件到窗口中快速加载
2025-06-30 12:29:35,684 - src.gui.main_window - INFO - 📖 使用帮助: 菜单栏 -> 帮助 -> 用户手册
2025-06-30 12:31:26,332 - src.gui.main_window - INFO - 应用程序退出
2025-06-30 12:31:41,211 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 12:31:41,217 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 12:31:41,218 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 12:31:41,237 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 12:31:41,238 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-30 12:31:41,238 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-30 12:31:41,256 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-30 12:31:41,257 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-30 12:31:41,257 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-30 12:31:41,257 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 12:31:41,276 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 12:31:41,276 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 12:31:41,276 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 12:31:41,276 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-30 12:31:41,277 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-30 12:31:41,277 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-30 12:31:41,277 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-30 12:31:41,277 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-30 12:31:41,277 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 12:31:41,277 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 12:31:41,277 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 12:31:41,277 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 12:31:41,277 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-30 12:31:41,278 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-30 12:31:41,278 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-30 12:31:41,278 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-30 12:31:41,278 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-30 12:31:41,278 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-30 12:31:41,278 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-30 12:31:41,279 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-30 12:31:41,279 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-30 12:31:41,280 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-30 12:31:41,782 - src.gui.main_window - INFO - 主窗口初始化完成
2025-06-30 12:31:41,783 - __main__ - INFO - 视频融合编辑器启动
2025-06-30 12:31:41,783 - __main__ - INFO - Python版本: 3.9.23 (main, Jun  5 2025, 08:23:30) 
[Clang 14.0.6 ]
2025-06-30 12:31:41,783 - __main__ - INFO - PyQt5版本: 5.15.10
2025-06-30 12:31:41,795 - src.gui.main_window - INFO - 🎉 欢迎使用视频融合编辑器！
2025-06-30 12:31:41,796 - src.gui.main_window - INFO - 💡 提示: 可以拖拽视频文件到窗口中快速加载
2025-06-30 12:31:41,796 - src.gui.main_window - INFO - 📖 使用帮助: 菜单栏 -> 帮助 -> 用户手册
2025-06-30 12:31:55,257 - src.gui.main_window - INFO - 应用程序退出
2025-06-30 13:35:54,873 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 13:35:54,873 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 13:35:54,874 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 13:35:54,874 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 13:35:54,874 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-30 13:35:54,874 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-30 13:35:54,874 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-30 13:35:54,874 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-30 13:35:54,874 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-30 13:35:54,875 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 13:35:54,875 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 13:35:54,875 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 13:35:54,875 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 13:35:54,875 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-30 13:35:54,875 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-30 13:35:54,875 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-30 13:35:54,875 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-30 13:35:54,875 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-30 13:35:54,875 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 13:35:54,876 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 13:35:54,876 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 13:35:54,876 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 13:35:54,876 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-30 13:35:54,876 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-30 13:35:54,876 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-30 13:35:54,876 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-30 13:35:54,876 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-30 13:35:54,876 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-30 13:35:54,876 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-30 13:35:54,877 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-30 13:35:54,877 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-30 13:35:54,877 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-30 13:35:55,367 - src.gui.main_window - INFO - 主窗口初始化完成
2025-06-30 13:35:55,368 - __main__ - INFO - 视频融合编辑器启动
2025-06-30 13:35:55,368 - __main__ - INFO - Python版本: 3.9.23 (main, Jun  5 2025, 08:23:30) 
[Clang 14.0.6 ]
2025-06-30 13:35:55,368 - __main__ - INFO - PyQt5版本: 5.15.10
2025-06-30 13:35:55,377 - src.gui.main_window - INFO - 🎉 欢迎使用视频融合编辑器！
2025-06-30 13:35:55,377 - src.gui.main_window - INFO - 💡 提示: 可以拖拽视频文件到窗口中快速加载
2025-06-30 13:35:55,378 - src.gui.main_window - INFO - 📖 使用帮助: 菜单栏 -> 帮助 -> 用户手册
2025-06-30 13:36:18,866 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 13:36:18,866 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 13:36:18,867 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 13:36:18,867 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 13:36:18,867 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-30 13:36:18,867 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-30 13:36:18,867 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-30 13:36:18,867 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-30 13:36:18,867 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-30 13:36:18,867 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 13:36:18,867 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 13:36:18,868 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 13:36:18,868 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 13:36:18,868 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-30 13:36:18,868 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-30 13:36:18,868 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-30 13:36:18,868 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-30 13:36:18,868 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-30 13:36:18,868 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 13:36:18,868 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 13:36:18,868 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 13:36:18,868 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 13:36:18,868 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-30 13:36:18,868 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-30 13:36:18,868 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-30 13:36:18,868 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-30 13:36:18,868 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-30 13:36:18,869 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-30 13:36:18,869 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-30 13:36:18,869 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-30 13:36:18,869 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-30 13:36:18,869 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-30 13:36:19,217 - src.gui.main_window - INFO - 主窗口初始化完成
2025-06-30 13:36:55,906 - src.gui.main_window - INFO - 应用程序退出
2025-06-30 13:37:18,317 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 13:37:18,318 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 13:37:18,318 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 13:37:18,318 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 13:37:18,318 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-30 13:37:18,318 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-30 13:37:18,319 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-30 13:37:18,319 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-30 13:37:18,319 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-30 13:37:18,319 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 13:37:18,319 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 13:37:18,319 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 13:37:18,319 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 13:37:18,319 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-30 13:37:18,319 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-30 13:37:18,319 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-30 13:37:18,319 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-30 13:37:18,320 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-30 13:37:18,320 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 13:37:18,320 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 13:37:18,320 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 13:37:18,320 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 13:37:18,320 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-30 13:37:18,320 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-30 13:37:18,320 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-30 13:37:18,320 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-30 13:37:18,320 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-30 13:37:18,321 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-30 13:37:18,321 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-30 13:37:18,321 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-30 13:37:18,321 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-30 13:37:18,321 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-30 13:37:18,805 - src.gui.main_window - INFO - 主窗口初始化完成
2025-06-30 13:37:18,806 - __main__ - INFO - 视频融合编辑器启动
2025-06-30 13:37:18,806 - __main__ - INFO - Python版本: 3.9.23 (main, Jun  5 2025, 08:23:30) 
[Clang 14.0.6 ]
2025-06-30 13:37:18,806 - __main__ - INFO - PyQt5版本: 5.15.10
2025-06-30 13:37:18,818 - src.gui.main_window - INFO - 🎉 欢迎使用视频融合编辑器！
2025-06-30 13:37:18,818 - src.gui.main_window - INFO - 💡 提示: 可以拖拽视频文件到窗口中快速加载
2025-06-30 13:37:18,818 - src.gui.main_window - INFO - 📖 使用帮助: 菜单栏 -> 帮助 -> 用户手册
2025-06-30 13:37:45,949 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 13:37:45,949 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 13:37:45,949 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 13:37:45,949 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 13:37:45,949 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-30 13:37:45,949 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-30 13:37:45,950 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-30 13:37:45,950 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-30 13:37:45,950 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-30 13:37:45,950 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 13:37:45,950 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 13:37:45,950 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 13:37:45,950 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 13:37:45,950 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-30 13:37:45,950 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-30 13:37:45,950 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-30 13:37:45,950 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-30 13:37:45,950 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-30 13:37:45,951 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 13:37:45,951 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 13:37:45,951 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 13:37:45,951 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 13:37:45,951 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-30 13:37:45,951 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-30 13:37:45,951 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-30 13:37:45,951 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-30 13:37:45,951 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-30 13:37:45,951 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-30 13:37:45,951 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-30 13:37:45,952 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-30 13:37:45,952 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-30 13:37:45,952 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-30 13:37:46,386 - src.gui.main_window - INFO - 主窗口初始化完成
2025-06-30 13:38:04,726 - src.gui.main_window - INFO - 应用程序退出
2025-06-30 13:38:19,241 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 13:38:19,241 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 13:38:19,241 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 13:38:19,241 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 13:38:19,242 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-30 13:38:19,242 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-30 13:38:19,242 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-30 13:38:19,242 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-30 13:38:19,242 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-30 13:38:19,242 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 13:38:19,242 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 13:38:19,242 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 13:38:19,242 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 13:38:19,242 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-30 13:38:19,243 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-30 13:38:19,243 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-30 13:38:19,243 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-30 13:38:19,243 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-30 13:38:19,243 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 13:38:19,243 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 13:38:19,243 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 13:38:19,243 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 13:38:19,243 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-30 13:38:19,243 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-30 13:38:19,243 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-30 13:38:19,243 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-30 13:38:19,243 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-30 13:38:19,244 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-30 13:38:19,244 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-30 13:38:19,244 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-30 13:38:19,244 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-30 13:38:19,244 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-30 13:38:19,680 - src.gui.main_window - INFO - 主窗口初始化完成
2025-06-30 13:38:19,681 - __main__ - INFO - 视频融合编辑器启动
2025-06-30 13:38:19,681 - __main__ - INFO - Python版本: 3.9.23 (main, Jun  5 2025, 08:23:30) 
[Clang 14.0.6 ]
2025-06-30 13:38:19,681 - __main__ - INFO - PyQt5版本: 5.15.10
2025-06-30 13:38:19,692 - src.gui.main_window - INFO - 🎉 欢迎使用视频融合编辑器！
2025-06-30 13:38:19,692 - src.gui.main_window - INFO - 💡 提示: 可以拖拽视频文件到窗口中快速加载
2025-06-30 13:38:19,692 - src.gui.main_window - INFO - 📖 使用帮助: 菜单栏 -> 帮助 -> 用户手册
2025-06-30 13:38:48,502 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 13:38:48,502 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 13:38:48,503 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 13:38:48,503 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 13:38:48,503 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-30 13:38:48,503 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-30 13:38:48,503 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-30 13:38:48,503 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-30 13:38:48,503 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-30 13:38:48,503 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 13:38:48,503 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 13:38:48,503 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 13:38:48,503 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 13:38:48,504 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-30 13:38:48,504 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-30 13:38:48,504 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-30 13:38:48,504 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-30 13:38:48,504 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-30 13:38:48,504 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 13:38:48,504 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 13:38:48,504 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-30 13:38:48,504 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-30 13:38:48,504 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-30 13:38:48,504 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-30 13:38:48,504 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-30 13:38:48,504 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-30 13:38:48,504 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-30 13:38:48,504 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-30 13:38:48,504 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-30 13:38:48,505 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-30 13:38:48,505 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-30 13:38:48,505 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-30 13:38:48,926 - src.gui.main_window - INFO - 主窗口初始化完成
