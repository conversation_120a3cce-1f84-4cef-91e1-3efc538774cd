"""
主窗口类
Main Window Class
"""

from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                             QMenuBar, QToolBar, QStatusBar, QSplitter,
                             QGroupBox, QLabel, QPushButton, QFileDialog,
                             QMessageBox, QProgressBar, QTextEdit, QTabWidget,
                             QAction, QApplication, QSpinBox, QCheckBox)
from PyQt5.QtCore import Qt, pyqtSignal, QThread, QTimer
from PyQt5.QtGui import QIcon, QPixmap, QDragEnterEvent, QDropEvent

from ..utils.config_manager import ConfigManager
from ..utils.logger import Logger
from ..fusion.fusion_engine import FusionEngine, FusionParams, FusionType
from ..video.video_loader import VideoLoader
from .control_panel import ControlPanel
from .preview_window import PreviewWindow
from .log_window import LogWindow
from .performance_window import PerformanceWindow
from .video_player import VideoPlayer
from .preset_manager import PresetManager
from .batch_dialog import BatchDialog


class FusionThread(QThread):
    """融合处理线程"""

    finished = pyqtSignal(bool)  # 完成信号
    error = pyqtSignal(str)      # 错误信号
    progress = pyqtSignal(int)   # 进度信号

    def __init__(self, fusion_engine: FusionEngine):
        super().__init__()
        self.fusion_engine = fusion_engine

    def run(self):
        """运行融合处理"""
        try:
            success = self.fusion_engine.execute_fusion()
            self.finished.emit(success)
        except Exception as e:
            self.error.emit(str(e))


class MainWindow(QMainWindow):
    """主窗口类"""
    
    # 信号定义
    video_a_loaded = pyqtSignal(str)  # A视频加载信号
    video_b_loaded = pyqtSignal(str)  # B视频加载信号
    
    def __init__(self):
        super().__init__()

        # 初始化配置和日志
        self.config = ConfigManager()
        self.logger = Logger.get_logger(__name__)

        # 初始化核心组件
        self.fusion_engine = FusionEngine()
        self.video_a_loader = VideoLoader()
        self.video_b_loader = VideoLoader()

        # 状态变量
        self.is_fusion_running = False
        self.current_video_a_path = None
        self.current_video_b_path = None
        self.current_preview_temp_path = None

        # 初始化UI
        self.init_ui()

        # 连接信号
        self.connect_signals()

        # 启用拖拽
        self.setAcceptDrops(True)

        self.logger.info("主窗口初始化完成")
    
    def init_ui(self):
        """初始化用户界面"""
        # 设置窗口属性
        self.setWindowTitle(self.config.get("application.name", "视频融合编辑器"))
        self.setGeometry(100, 100, 
                        self.config.get("application.window.width", 1200),
                        self.config.get("application.window.height", 800))
        self.setMinimumSize(self.config.get("application.window.min_width", 800),
                           self.config.get("application.window.min_height", 600))
        
        # 创建菜单栏
        self.create_menu_bar()
        
        # 创建工具栏
        self.create_tool_bar()
        
        # 创建中央部件
        self.create_central_widget()
        
        # 创建状态栏
        self.create_status_bar()
    
    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu('文件(&F)')

        # 新建项目
        new_project_action = file_menu.addAction('新建项目(&N)')
        new_project_action.setShortcut('Ctrl+N')
        new_project_action.triggered.connect(self.new_project)

        # 打开项目
        open_project_action = file_menu.addAction('打开项目(&O)')
        open_project_action.setShortcut('Ctrl+O')
        open_project_action.triggered.connect(self.open_project)

        # 保存项目
        save_project_action = file_menu.addAction('保存项目(&S)')
        save_project_action.setShortcut('Ctrl+S')
        save_project_action.triggered.connect(self.save_project)

        file_menu.addSeparator()

        # 加载视频子菜单
        load_video_menu = file_menu.addMenu('加载视频(&L)')

        # 加载A视频
        load_a_action = load_video_menu.addAction('加载A视频(&A)')
        load_a_action.setShortcut('Ctrl+Shift+A')
        load_a_action.triggered.connect(self.load_video_a)

        # 加载B视频
        load_b_action = load_video_menu.addAction('加载B视频(&B)')
        load_b_action.setShortcut('Ctrl+Shift+B')
        load_b_action.triggered.connect(self.load_video_b)

        file_menu.addSeparator()

        # 导出视频
        export_action = file_menu.addAction('导出视频(&E)')
        export_action.setShortcut('Ctrl+E')
        export_action.triggered.connect(self.export_video)

        # 批量处理
        batch_action = file_menu.addAction('批量处理(&T)')
        batch_action.setShortcut('Ctrl+T')
        batch_action.triggered.connect(self.batch_process)

        file_menu.addSeparator()

        # 退出
        exit_action = file_menu.addAction('退出(&X)')
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)
        
        # 编辑菜单
        edit_menu = menubar.addMenu('编辑(&E)')

        # 撤销
        undo_action = edit_menu.addAction('撤销(&U)')
        undo_action.setShortcut('Ctrl+Z')
        undo_action.setEnabled(False)  # 暂时禁用

        # 重做
        redo_action = edit_menu.addAction('重做(&R)')
        redo_action.setShortcut('Ctrl+Y')
        redo_action.setEnabled(False)  # 暂时禁用

        edit_menu.addSeparator()

        # 参数预设
        presets_action = edit_menu.addAction('参数预设(&P)')
        presets_action.setShortcut('Ctrl+P')
        presets_action.triggered.connect(self.open_preset_manager)

        # 融合菜单
        fusion_menu = menubar.addMenu('融合(&F)')

        # 开始融合
        start_fusion_action = fusion_menu.addAction('开始融合(&S)')
        start_fusion_action.setShortcut('F5')
        start_fusion_action.triggered.connect(self.start_fusion)

        # 停止融合
        stop_fusion_action = fusion_menu.addAction('停止融合(&T)')
        stop_fusion_action.setShortcut('F6')
        stop_fusion_action.triggered.connect(self.stop_fusion)

        fusion_menu.addSeparator()

        # 生成预览
        preview_action = fusion_menu.addAction('生成预览(&P)')
        preview_action.setShortcut('F7')
        preview_action.triggered.connect(self.generate_preview)

        # 视图菜单
        view_menu = menubar.addMenu('视图(&V)')

        # 重置布局
        reset_layout_action = view_menu.addAction('重置布局(&R)')
        reset_layout_action.triggered.connect(self.reset_layout)

        view_menu.addSeparator()

        # 预览窗口
        preview_window_action = view_menu.addAction('预览窗口(&P)')
        preview_window_action.setShortcut('Ctrl+1')
        preview_window_action.triggered.connect(self.show_preview_window)

        # 日志窗口
        log_window_action = view_menu.addAction('日志窗口(&L)')
        log_window_action.setShortcut('Ctrl+2')
        log_window_action.triggered.connect(self.show_log_window)

        # 性能监控窗口
        performance_window_action = view_menu.addAction('性能监控窗口(&M)')
        performance_window_action.setShortcut('Ctrl+3')
        performance_window_action.triggered.connect(self.show_performance_window)

        # 工具菜单
        tools_menu = menubar.addMenu('工具(&T)')

        # 性能报告
        performance_report_action = tools_menu.addAction('导出性能报告(&R)')
        performance_report_action.triggered.connect(self.export_performance_report)

        # 帮助菜单
        help_menu = menubar.addMenu('帮助(&H)')

        # 用户手册
        manual_action = help_menu.addAction('用户手册(&M)')
        manual_action.setShortcut('F1')
        manual_action.triggered.connect(self.show_manual)

        help_menu.addSeparator()

        # 关于
        about_action = help_menu.addAction('关于(&A)')
        about_action.triggered.connect(self.show_about)
    
    def create_tool_bar(self):
        """创建工具栏"""
        # 主工具栏 - 只保留必要的状态显示，移除重复按钮
        main_toolbar = self.addToolBar('主工具栏')
        main_toolbar.setMovable(False)

        # 可以在这里添加状态指示器或其他非重复功能
        # 暂时保持空白，所有功能通过菜单访问
    
    def create_central_widget(self):
        """创建中央部件"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QHBoxLayout(central_widget)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧视频区域
        self.create_video_area(splitter)
        
        # 右侧控制面板
        self.create_control_panel(splitter)
        
        # 设置分割器比例
        splitter.setSizes([800, 400])
    
    def create_video_area(self, parent):
        """创建视频显示区域"""
        video_widget = QWidget()
        video_layout = QVBoxLayout(video_widget)

        # A视频播放器
        self.video_a_player = VideoPlayer("A视频 (源视频)")
        self.video_a_player.position_changed.connect(self.on_video_a_position_changed)
        self.video_a_player.play_state_changed.connect(self.on_video_a_play_state_changed)
        video_layout.addWidget(self.video_a_player)

        # B视频播放器
        self.video_b_player = VideoPlayer("B视频 (目标视频)")
        self.video_b_player.position_changed.connect(self.on_video_b_position_changed)
        self.video_b_player.play_state_changed.connect(self.on_video_b_play_state_changed)
        video_layout.addWidget(self.video_b_player)

        # 融合后视频播放器
        self.fusion_result_player = VideoPlayer("融合后视频 (预览结果)")
        self.fusion_result_player.position_changed.connect(self.on_fusion_result_position_changed)
        self.fusion_result_player.play_state_changed.connect(self.on_fusion_result_play_state_changed)
        video_layout.addWidget(self.fusion_result_player)

        # 融合后视频控制区域
        fusion_control_widget = QWidget()
        fusion_control_layout = QHBoxLayout(fusion_control_widget)

        # 预览控制按钮
        self.generate_preview_btn = QPushButton("生成预览")
        self.generate_preview_btn.clicked.connect(self.generate_fusion_preview)
        self.generate_preview_btn.setToolTip("生成融合预览视频")
        fusion_control_layout.addWidget(self.generate_preview_btn)

        self.clear_preview_btn = QPushButton("清除预览")
        self.clear_preview_btn.clicked.connect(self.clear_fusion_preview)
        self.clear_preview_btn.setToolTip("清除当前预览")
        self.clear_preview_btn.setEnabled(False)  # 初始状态禁用
        fusion_control_layout.addWidget(self.clear_preview_btn)

        # 添加弹性空间
        fusion_control_layout.addStretch()

        video_layout.addWidget(fusion_control_widget)

        parent.addWidget(video_widget)
    
    def create_control_panel(self, parent):
        """创建控制面板"""
        # 直接使用融合参数控制面板，不再使用tab
        self.control_panel = ControlPanel()
        parent.addWidget(self.control_panel)

        # 连接参数变化信号
        self.control_panel.parameters_changed.connect(self.on_parameters_changed)

        # 初始化独立窗口变量
        self.preview_window = None
        self.log_window = None
        self.performance_window = None

        # 创建独立窗口实例
        self.create_independent_windows()

    def create_independent_windows(self):
        """创建独立窗口"""
        # 预览窗口
        self.preview_window = PreviewWindow(self)
        self.preview_window.preview_requested.connect(self.generate_preview)
        self.preview_window.preview_cleared.connect(self.on_preview_cleared)

        # 日志窗口
        self.log_window = LogWindow(self)
        self.log_window.log_cleared.connect(self.clear_log)
        self.log_window.log_exported.connect(self.on_log_exported)

        # 性能监控窗口
        self.performance_window = PerformanceWindow(self)
        self.performance_window.performance_exported.connect(self.on_performance_exported)

    def show_preview_window(self):
        """显示预览窗口"""
        if self.preview_window:
            self.preview_window.show()
            self.preview_window.raise_()
            self.preview_window.activateWindow()

    def show_log_window(self):
        """显示日志窗口"""
        if self.log_window:
            self.log_window.show()
            self.log_window.raise_()
            self.log_window.activateWindow()

    def show_performance_window(self):
        """显示性能监控窗口"""
        if self.performance_window:
            self.performance_window.show()
            self.performance_window.raise_()
            self.performance_window.activateWindow()

    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = self.statusBar()
        
        # 状态标签
        self.status_label = QLabel("就绪")
        self.status_bar.addWidget(self.status_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.status_bar.addPermanentWidget(self.progress_bar)
    
    def connect_signals(self):
        """连接信号"""
        # 视频加载信号
        self.video_a_loaded.connect(self.on_video_a_loaded)
        self.video_b_loaded.connect(self.on_video_b_loaded)

        # 控制面板信号
        if hasattr(self, 'control_panel'):
            self.control_panel.parameters_changed.connect(self.on_parameters_changed)
            self.control_panel.preview_requested.connect(self.generate_preview)
            self.control_panel.fusion_requested.connect(self.start_fusion)

        # 性能监控由独立窗口处理，不再需要定时器
    
    def load_video_a(self):
        """加载A视频"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择A视频文件", "",
            "视频文件 (*.mp4 *.avi *.mov *.mkv *.wmv);;所有文件 (*)"
        )

        if file_path:
            self.load_video_a_file(file_path)

    def load_video_b(self):
        """加载B视频"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择B视频文件", "",
            "视频文件 (*.mp4 *.avi *.mov *.mkv *.wmv);;所有文件 (*)"
        )

        if file_path:
            self.load_video_b_file(file_path)

    def on_video_a_position_changed(self, frame_number: int):
        """A视频播放位置改变"""
        self.log_message(f"A视频播放位置: 第{frame_number}帧")

    def on_video_a_play_state_changed(self, is_playing: bool):
        """A视频播放状态改变"""
        state = "播放" if is_playing else "暂停"
        self.log_message(f"A视频播放状态: {state}")

    def on_video_b_position_changed(self, frame_number: int):
        """B视频播放位置改变"""
        self.log_message(f"B视频播放位置: 第{frame_number}帧")

    def on_video_b_play_state_changed(self, is_playing: bool):
        """B视频播放状态改变"""
        state = "播放" if is_playing else "暂停"
        self.log_message(f"B视频播放状态: {state}")

    def on_fusion_result_position_changed(self, frame_number: int):
        """融合后视频播放位置改变"""
        self.log_message(f"融合后视频播放位置: 第{frame_number}帧")

    def on_fusion_result_play_state_changed(self, is_playing: bool):
        """融合后视频播放状态改变"""
        state = "播放" if is_playing else "暂停"
        self.log_message(f"融合后视频播放状态: {state}")

    def load_video_a_file(self, file_path: str):
        """加载A视频文件"""
        try:
            self.log_message(f"正在加载A视频: {file_path}")

            # 首先加载到原有的融合引擎（这是核心功能）
            video_info = self.video_a_loader.load_video(file_path)
            if video_info:
                self.current_video_a_path = file_path
                self.fusion_engine.load_video_a(file_path)
                self.video_a_loaded.emit(file_path)
                self.log_message(f"A视频融合引擎加载成功: {file_path}")

                # 尝试加载到VideoPlayer组件（用于播放控制）
                if not self.video_a_player.load_video(file_path):
                    self.log_message(f"A视频播放器加载失败，但融合功能正常: {file_path}", "WARNING")

                self.log_message(f"A视频加载成功: {file_path}")
            else:
                self.log_message(f"A视频加载失败: {file_path}", "ERROR")
                QMessageBox.warning(self, "错误", f"无法加载A视频文件:\n{file_path}")

        except Exception as e:
            self.log_message(f"A视频加载异常: {e}", "ERROR")
            QMessageBox.critical(self, "错误", f"加载A视频时发生错误:\n{str(e)}")

    def load_video_b_file(self, file_path: str):
        """加载B视频文件"""
        try:
            self.log_message(f"正在加载B视频: {file_path}")

            # 首先加载到原有的融合引擎（这是核心功能）
            video_info = self.video_b_loader.load_video(file_path)
            if video_info:
                self.current_video_b_path = file_path
                self.fusion_engine.load_video_b(file_path)
                self.video_b_loaded.emit(file_path)
                self.log_message(f"B视频融合引擎加载成功: {file_path}")

                # 尝试加载到VideoPlayer组件（用于播放控制）
                if not self.video_b_player.load_video(file_path):
                    self.log_message(f"B视频播放器加载失败，但融合功能正常: {file_path}", "WARNING")

                self.log_message(f"B视频加载成功: {file_path}")
            else:
                self.log_message(f"B视频加载失败: {file_path}", "ERROR")
                QMessageBox.warning(self, "错误", f"无法加载B视频文件:\n{file_path}")

        except Exception as e:
            self.log_message(f"B视频加载异常: {e}", "ERROR")
            QMessageBox.critical(self, "错误", f"加载B视频时发生错误:\n{str(e)}")
    
    def dragEnterEvent(self, event: QDragEnterEvent):
        """拖拽进入事件"""
        if event.mimeData().hasUrls():
            event.accept()
        else:
            event.ignore()

    def dropEvent(self, event: QDropEvent):
        """拖拽放下事件"""
        files = [u.toLocalFile() for u in event.mimeData().urls()]

        if files:
            # 检查文件扩展名
            video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv']
            video_files = [f for f in files if any(f.lower().endswith(ext) for ext in video_extensions)]

            if video_files:
                if len(video_files) == 1:
                    # 只有一个视频文件，询问用户要加载到A还是B
                    reply = QMessageBox.question(
                        self, "选择加载位置",
                        f"将视频文件加载到:\n{video_files[0]}\n\n选择'Yes'加载到A视频，'No'加载到B视频",
                        QMessageBox.Yes | QMessageBox.No | QMessageBox.Cancel
                    )

                    if reply == QMessageBox.Yes:
                        self.load_video_a_file(video_files[0])
                    elif reply == QMessageBox.No:
                        self.load_video_b_file(video_files[0])

                elif len(video_files) >= 2:
                    # 多个视频文件，自动分配
                    self.load_video_a_file(video_files[0])
                    self.load_video_b_file(video_files[1])

                    if len(video_files) > 2:
                        self.log_message(f"检测到{len(video_files)}个视频文件，只加载前两个", "WARNING")
            else:
                QMessageBox.warning(self, "错误", "拖拽的文件中没有支持的视频格式")

    def on_video_a_loaded(self, file_path: str):
        """A视频加载完成"""
        import os
        filename = os.path.basename(file_path)
        self.status_label.setText(f"A视频已加载: {filename}")

        # 显示视频信息
        if self.video_a_loader.is_loaded():
            info = self.video_a_loader.get_current_info()
            self.log_message(f"A视频信息: {info.width}x{info.height}, "
                                         f"帧率: {info.fps:.2f} FPS, "
                                         f"时长: {info.duration:.2f}秒")

        self.update_ui_state()

    def on_video_b_loaded(self, file_path: str):
        """B视频加载完成"""
        import os
        filename = os.path.basename(file_path)
        self.status_label.setText(f"B视频已加载: {filename}")

        # 显示视频信息
        if self.video_b_loader.is_loaded():
            info = self.video_b_loader.get_current_info()
            self.log_message(f"B视频信息: {info.width}x{info.height}, "
                                         f"帧率: {info.fps:.2f} FPS, "
                                         f"时长: {info.duration:.2f}秒")

        self.update_ui_state()
    
    def update_ui_state(self):
        """更新UI状态"""
        # 检查是否可以开始融合
        can_start_fusion = (bool(self.current_video_a_path) and
                           bool(self.current_video_b_path) and
                           not self.is_fusion_running)

        # 工具栏按钮已删除，不需要更新按钮状态
        # 所有功能通过菜单访问

        # 更新预览窗口按钮状态
        if self.preview_window:
            self.preview_window.set_preview_enabled(can_start_fusion)

        # 更新融合预览按钮状态
        if hasattr(self, 'generate_preview_btn'):
            self.generate_preview_btn.setEnabled(can_start_fusion)

        # 更新预览相关按钮状态
        has_preview = (hasattr(self, 'current_preview_temp_path') and
                      self.current_preview_temp_path is not None)
        if hasattr(self, 'clear_preview_btn'):
            self.clear_preview_btn.setEnabled(has_preview)

    def on_parameters_changed(self, params: dict):
        """参数变化处理 - 更新为五维控制系统"""
        try:
            # 使用新的GUI参数设置方法
            self.fusion_engine.set_fusion_params_from_gui(params)

            # 为插入融合设置默认位置（如果没有指定）
            fusion_type = params.get('fusion_type', 'insertion')
            if fusion_type == 'insertion' and not self.fusion_engine.fusion_params.positions:
                # 生成一些默认的插入位置用于预览
                if self.current_video_b_path:
                    try:
                        from src.fusion.insertion_fusion import InsertionPosition
                        # 在B视频的几个位置插入A视频
                        b_info = self.video_b_loader.get_current_info() if self.video_b_loader else None
                        if b_info and b_info.frame_count > 0:
                            # 在视频的25%, 50%, 75%位置插入
                            positions = []
                            for ratio in [0.25, 0.5, 0.75]:
                                frame_num = int(b_info.frame_count * ratio)
                                pos = InsertionPosition(
                                    frame_number=frame_num,
                                    duration=1  # 插入1帧
                                )
                                positions.append(pos)
                            self.fusion_engine.fusion_params.positions = positions
                            self.log_message(f"设置默认插入位置: {len(positions)}个位置")
                    except Exception as e:
                        self.log_message(f"设置默认插入位置失败: {e}", "WARNING")

            self.log_message("融合参数已更新")

        except Exception as e:
            self.log_message(f"参数更新失败: {e}", "ERROR")

    def diagnose_video_loading_issues(self):
        """诊断视频加载问题"""
        issues = []
        details = []

        # 检查A视频
        if not self.current_video_a_path:
            issues.append("A视频未加载")
            details.append("- A视频路径为空")
        else:
            details.append(f"- A视频路径: {self.current_video_a_path}")

            # 检查A视频加载器状态
            if not self.video_a_loader.is_loaded():
                issues.append("A视频加载器状态异常")
                details.append("- A视频加载器显示未加载")
            else:
                a_info = self.video_a_loader.get_current_info()
                if a_info:
                    details.append(f"- A视频信息: {a_info.width}x{a_info.height}, {a_info.frame_count}帧")
                else:
                    issues.append("A视频信息获取失败")
                    details.append("- A视频信息为空")

            # 检查A视频播放器状态
            if hasattr(self, 'video_a_player'):
                if not self.video_a_player.is_video_loaded():
                    details.append("- A视频播放器未加载（不影响融合功能）")
                else:
                    details.append(f"- A视频播放器: {self.video_a_player.get_total_frames()}帧")

        # 检查B视频
        if not self.current_video_b_path:
            issues.append("B视频未加载")
            details.append("- B视频路径为空")
        else:
            details.append(f"- B视频路径: {self.current_video_b_path}")

            # 检查B视频加载器状态
            if not self.video_b_loader.is_loaded():
                issues.append("B视频加载器状态异常")
                details.append("- B视频加载器显示未加载")
            else:
                b_info = self.video_b_loader.get_current_info()
                if b_info:
                    details.append(f"- B视频信息: {b_info.width}x{b_info.height}, {b_info.frame_count}帧")
                else:
                    issues.append("B视频信息获取失败")
                    details.append("- B视频信息为空")

            # 检查B视频播放器状态
            if hasattr(self, 'video_b_player'):
                if not self.video_b_player.is_video_loaded():
                    details.append("- B视频播放器未加载（不影响融合功能）")
                else:
                    details.append(f"- B视频播放器: {self.video_b_player.get_total_frames()}帧")

        # 检查融合引擎状态
        if hasattr(self, 'fusion_engine'):
            try:
                engine_status = self.fusion_engine.get_status()
                details.append(f"- 融合引擎状态: {engine_status}")
            except:
                details.append("- 融合引擎状态检查失败")

        return issues, details

    def start_fusion(self):
        """开始融合"""
        if not self.current_video_a_path or not self.current_video_b_path:
            # 进行详细诊断
            issues, details = self.diagnose_video_loading_issues()

            # 构建详细错误信息
            error_msg = "无法开始融合，发现以下问题：\n\n"

            if issues:
                error_msg += "问题列表：\n"
                for i, issue in enumerate(issues, 1):
                    error_msg += f"{i}. {issue}\n"
                error_msg += "\n"

            error_msg += "详细状态：\n"
            for detail in details:
                error_msg += f"{detail}\n"

            error_msg += "\n解决建议：\n"
            if not self.current_video_a_path:
                error_msg += "• 请点击'加载A视频'按钮选择源视频文件\n"
            if not self.current_video_b_path:
                error_msg += "• 请点击'加载B视频'按钮选择目标视频文件\n"

            if "加载器状态异常" in str(issues):
                error_msg += "• 如果文件已选择但加载失败，请检查：\n"
                error_msg += "  - 视频文件是否存在且可访问\n"
                error_msg += "  - 视频格式是否支持（推荐MP4格式）\n"
                error_msg += "  - 视频文件是否损坏\n"

            # 记录详细日志
            self.log_message("融合启动失败 - 详细诊断信息:", "ERROR")
            for detail in details:
                self.log_message(detail, "ERROR")

            QMessageBox.warning(self, "无法开始融合", error_msg)
            return

        if self.is_fusion_running:
            QMessageBox.warning(self, "警告", "融合正在进行中")
            return

        try:
            self.is_fusion_running = True
            self.update_ui_state()

            self.status_label.setText("正在执行融合...")
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)  # 不确定进度

            self.log_message("开始融合处理...")

            # 在后台线程中执行融合
            self.fusion_thread = FusionThread(self.fusion_engine)
            self.fusion_thread.finished.connect(self.on_fusion_finished)
            self.fusion_thread.error.connect(self.on_fusion_error)
            self.fusion_thread.start()

        except Exception as e:
            self.is_fusion_running = False
            self.update_ui_state()
            self.log_message(f"启动融合失败: {e}", "ERROR")
            QMessageBox.critical(self, "错误", f"启动融合时发生错误:\n{str(e)}")

    def stop_fusion(self):
        """停止融合"""
        if not self.is_fusion_running:
            return

        try:
            if hasattr(self, 'fusion_thread') and self.fusion_thread.isRunning():
                self.fusion_thread.terminate()
                self.fusion_thread.wait(3000)  # 等待3秒

            self.is_fusion_running = False
            self.update_ui_state()

            self.status_label.setText("融合已停止")
            self.progress_bar.setVisible(False)
            self.log_message("融合处理已停止")

        except Exception as e:
            self.log_message(f"停止融合失败: {e}", "ERROR")
    
    def on_fusion_finished(self, success: bool):
        """融合完成处理"""
        self.is_fusion_running = False
        self.update_ui_state()
        self.progress_bar.setVisible(False)

        if success:
            self.status_label.setText("融合处理完成")
            self.log_message("融合处理成功完成")
            QMessageBox.information(self, "成功", "视频融合处理完成！")
        else:
            self.status_label.setText("融合处理失败")
            self.log_message("融合处理失败", "ERROR")
            QMessageBox.warning(self, "失败", "视频融合处理失败，请检查日志")

    def on_fusion_error(self, error_msg: str):
        """融合错误处理"""
        self.is_fusion_running = False
        self.update_ui_state()
        self.progress_bar.setVisible(False)

        self.status_label.setText("融合处理出错")
        self.log_message(f"融合处理错误: {error_msg}", "ERROR")
        QMessageBox.critical(self, "错误", f"融合处理时发生错误:\n{error_msg}")

    def generate_preview(self):
        """生成预览"""
        if not self.current_video_a_path or not self.current_video_b_path:
            # 进行详细诊断
            issues, details = self.diagnose_video_loading_issues()

            # 构建详细错误信息
            error_msg = "无法生成预览，发现以下问题：\n\n"

            if issues:
                error_msg += "问题列表：\n"
                for i, issue in enumerate(issues, 1):
                    error_msg += f"{i}. {issue}\n"
                error_msg += "\n"

            error_msg += "详细状态：\n"
            for detail in details:
                error_msg += f"{detail}\n"

            error_msg += "\n解决建议：\n"
            if not self.current_video_a_path:
                error_msg += "• 请点击'加载A视频'按钮选择源视频文件\n"
            if not self.current_video_b_path:
                error_msg += "• 请点击'加载B视频'按钮选择目标视频文件\n"

            if "加载器状态异常" in str(issues):
                error_msg += "• 如果文件已选择但加载失败，请检查：\n"
                error_msg += "  - 视频文件是否存在且可访问\n"
                error_msg += "  - 视频格式是否支持（推荐MP4格式）\n"
                error_msg += "  - 视频文件是否损坏\n"

            # 记录详细日志
            self.log_message("预览生成失败 - 详细诊断信息:", "ERROR")
            for detail in details:
                self.log_message(detail, "ERROR")

            QMessageBox.warning(self, "无法生成预览", error_msg)
            return

        try:
            self.log_message("正在生成预览...")
            self.status_label.setText("正在生成预览...")

            # 获取预览帧
            preview_frames = self.fusion_engine.get_fusion_preview(max_frames=3)

            if preview_frames:
                # 显示第一个预览帧
                frame_index, frame = preview_frames[0]

                # 发送到预览窗口
                if self.preview_window:
                    self.preview_window.update_preview(frame, frame_index)

                self.log_message(f"预览生成完成，显示第{frame_index}帧")
                self.status_label.setText("预览生成完成")

                # 如果预览窗口未显示，自动显示
                if self.preview_window and not self.preview_window.isVisible():
                    self.show_preview_window()
            else:
                self.log_message("预览生成失败：无预览帧", "WARNING")
                QMessageBox.warning(self, "警告", "无法生成预览，请检查融合参数")

        except Exception as e:
            self.log_message(f"预览生成失败: {e}", "ERROR")
            QMessageBox.critical(self, "错误", f"生成预览时发生错误:\n{str(e)}")

    def generate_fusion_preview(self):
        """生成融合预览到融合后视频播放器"""
        if not self.current_video_a_path or not self.current_video_b_path:
            # 使用详细诊断功能
            issues, details = self.diagnose_video_loading_issues()

            if issues:
                error_msg = "无法生成预览，发现以下问题：\n\n"
                error_msg += "问题列表：\n"
                for i, issue in enumerate(issues, 1):
                    error_msg += f"{i}. {issue}\n"

                error_msg += "\n详细状态：\n"
                for detail in details:
                    error_msg += f"{detail}\n"

                error_msg += "\n解决建议：\n"
                error_msg += "• 请使用菜单或拖拽方式加载A视频和B视频\n"
                error_msg += "• 确保视频文件格式正确（支持mp4、avi、mov等）\n"
                error_msg += "• 检查视频文件是否损坏\n"

                self.log_message(f"预览生成失败: {', '.join(issues)}", "WARNING")
                QMessageBox.warning(self, "无法生成预览", error_msg)
                return

        try:
            self.log_message("正在生成融合预览...")
            self.status_label.setText("正在生成融合预览...")

            # 获取预览帧（固定3帧）
            preview_frames = self.fusion_engine.get_fusion_preview(max_frames=3)

            if preview_frames:
                # 将预览帧转换为视频序列并加载到融合后视频播放器
                self.load_preview_to_player(preview_frames)

                self.log_message(f"融合预览生成完成，共{len(preview_frames)}帧")
                self.status_label.setText("融合预览生成完成")

                # 更新按钮状态
                self.update_preview_buttons_state(True)

            else:
                self.log_message("融合预览生成失败：无预览帧", "WARNING")

                # 检查具体原因
                if not self.current_video_a_path or not self.current_video_b_path:
                    QMessageBox.warning(self, "警告", "无法生成预览：请先加载A视频和B视频")
                else:
                    QMessageBox.warning(self, "警告", "无法生成预览，请检查融合参数设置")

        except Exception as e:
            self.log_message(f"融合预览生成失败: {e}", "ERROR")
            QMessageBox.critical(self, "错误", f"生成融合预览时发生错误:\n{str(e)}")

    def load_preview_to_player(self, preview_frames):
        """将预览帧加载到融合后视频播放器"""
        try:
            # 创建临时视频文件
            import tempfile
            import cv2
            import os

            # 创建临时文件
            temp_dir = tempfile.mkdtemp()
            temp_video_path = os.path.join(temp_dir, "fusion_preview.mp4")

            if preview_frames:
                # 获取第一帧的尺寸
                first_frame = preview_frames[0][1]
                height, width = first_frame.shape[:2]

                # 创建视频写入器
                fourcc = cv2.VideoWriter_fourcc(*'mp4v')
                fps = 30  # 预览视频帧率
                out = cv2.VideoWriter(temp_video_path, fourcc, fps, (width, height))

                # 写入所有预览帧
                for frame_index, frame in preview_frames:
                    # 每帧重复写入多次以延长显示时间
                    for _ in range(30):  # 每帧显示1秒
                        out.write(frame)

                out.release()

                # 加载到融合后视频播放器
                if os.path.exists(temp_video_path):
                    success = self.fusion_result_player.load_video(temp_video_path)
                    if success:
                        self.log_message(f"预览视频已加载到融合后视频播放器")
                        # 保存临时文件路径，用于后续清理
                        self.current_preview_temp_path = temp_video_path
                    else:
                        self.log_message("预览视频加载到播放器失败", "WARNING")
                        # 清理临时文件
                        if os.path.exists(temp_video_path):
                            os.remove(temp_video_path)
                        os.rmdir(temp_dir)
                else:
                    self.log_message("预览视频文件创建失败", "ERROR")

        except Exception as e:
            self.log_message(f"预览视频加载失败: {e}", "ERROR")

    def clear_fusion_preview(self):
        """清除融合预览"""
        try:
            # 清除播放器内容
            self.fusion_result_player.clear_video()

            # 清理临时文件
            if hasattr(self, 'current_preview_temp_path') and self.current_preview_temp_path:
                import os
                if os.path.exists(self.current_preview_temp_path):
                    os.remove(self.current_preview_temp_path)
                    temp_dir = os.path.dirname(self.current_preview_temp_path)
                    if os.path.exists(temp_dir):
                        os.rmdir(temp_dir)
                self.current_preview_temp_path = None

            # 更新按钮状态
            self.update_preview_buttons_state(False)

            self.log_message("融合预览已清除")
            self.status_label.setText("融合预览已清除")

        except Exception as e:
            self.log_message(f"清除预览失败: {e}", "ERROR")



    def update_preview_buttons_state(self, has_preview: bool):
        """更新预览按钮状态"""
        self.clear_preview_btn.setEnabled(has_preview)





    def export_video(self):
        """导出视频"""
        if not hasattr(self.fusion_engine, 'result_frames') or not self.fusion_engine.result_frames:
            QMessageBox.warning(self, "警告", "没有可导出的融合结果，请先执行融合")
            return

        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存融合视频", "",
            "MP4文件 (*.mp4);;AVI文件 (*.avi);;所有文件 (*)"
        )

        if file_path:
            try:
                self.log_message(f"正在导出视频到: {file_path}")
                self.status_label.setText("正在导出视频...")
                self.progress_bar.setVisible(True)
                self.progress_bar.setRange(0, 0)

                # 执行导出
                success = self.fusion_engine.save_result(file_path)

                self.progress_bar.setVisible(False)

                if success:
                    self.log_message(f"视频导出成功: {file_path}")
                    self.status_label.setText("视频导出完成")
                    QMessageBox.information(self, "成功", f"视频已成功导出到:\n{file_path}")
                else:
                    self.log_message(f"视频导出失败: {file_path}", "ERROR")
                    QMessageBox.warning(self, "失败", "视频导出失败，请检查文件路径和权限")

            except Exception as e:
                self.progress_bar.setVisible(False)
                self.log_message(f"视频导出异常: {e}", "ERROR")
                QMessageBox.critical(self, "错误", f"导出视频时发生错误:\n{str(e)}")
    
    def log_message(self, message: str, level: str = "INFO"):
        """添加日志消息"""
        import datetime
        timestamp = datetime.datetime.now()

        # 记录到系统日志
        if level == "ERROR":
            self.logger.error(message)
        elif level == "WARNING":
            self.logger.warning(message)
        else:
            self.logger.info(message)

        # 发送到日志窗口
        if self.log_window:
            self.log_window.add_log(level, message, timestamp)

        # 在状态栏显示最新消息
        status_msg = f"[{timestamp.strftime('%H:%M:%S')}] {message}"
        self.status_bar.showMessage(status_msg, 5000)  # 显示5秒

    def clear_log(self):
        """清除日志"""
        if self.log_window:
            self.log_window.clear_logs()
        self.log_message("日志已清除")

    def clear_preview(self):
        """清除预览（主动调用）"""
        if self.preview_window:
            self.preview_window.clear_preview()
        # 注意：不在这里记录日志，避免重复记录

    def on_preview_cleared(self):
        """预览清除完成回调"""
        self.log_message("预览已清除")

    def on_log_exported(self, file_path):
        """日志导出完成回调"""
        self.log_message(f"日志已导出到: {file_path}")

    def on_performance_exported(self, file_path):
        """性能报告导出完成回调"""
        self.log_message(f"性能报告已导出到: {file_path}")

    def export_log(self):
        """导出日志"""
        if not hasattr(self, 'log_text'):
            return

        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存日志文件", "",
            "文本文件 (*.txt);;所有文件 (*)"
        )

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(self.log_text.toPlainText())

                self.log_message(f"日志已导出到: {file_path}")
                QMessageBox.information(self, "成功", f"日志已导出到:\n{file_path}")

            except Exception as e:
                self.log_message(f"日志导出失败: {e}", "ERROR")
                QMessageBox.critical(self, "错误", f"导出日志时发生错误:\n{str(e)}")

    def export_performance_report(self):
        """导出性能报告"""
        if self.performance_window:
            self.performance_window.export_report()
        else:
            self.log_message("性能监控窗口未初始化", "WARNING")

    # 项目管理方法
    def new_project(self):
        """新建项目"""
        reply = QMessageBox.question(
            self, "新建项目",
            "创建新项目将清除当前所有设置，是否继续？",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.reset_project()
            self.log_message("新项目已创建")

    def open_project(self):
        """打开项目"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "打开项目文件", "",
            "项目文件 (*.json);;所有文件 (*)"
        )

        if file_path:
            try:
                # TODO: 实现项目加载逻辑
                self.log_message(f"项目加载功能开发中: {file_path}", "WARNING")
                QMessageBox.information(self, "提示", "项目加载功能正在开发中")

            except Exception as e:
                self.log_message(f"项目加载失败: {e}", "ERROR")
                QMessageBox.critical(self, "错误", f"加载项目时发生错误:\n{str(e)}")

    def save_project(self):
        """保存项目"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存项目文件", "",
            "项目文件 (*.json);;所有文件 (*)"
        )

        if file_path:
            try:
                # TODO: 实现项目保存逻辑
                self.log_message(f"项目保存功能开发中: {file_path}", "WARNING")
                QMessageBox.information(self, "提示", "项目保存功能正在开发中")

            except Exception as e:
                self.log_message(f"项目保存失败: {e}", "ERROR")
                QMessageBox.critical(self, "错误", f"保存项目时发生错误:\n{str(e)}")

    def batch_process(self):
        """批量处理"""
        try:
            batch_dialog = BatchDialog(self)
            batch_dialog.exec_()

        except Exception as e:
            self.log_message(f"打开批量处理对话框失败: {e}", "ERROR")
            QMessageBox.critical(self, "错误", f"打开批量处理对话框时发生错误:\n{str(e)}")

    def open_preset_manager(self):
        """打开预设管理器"""
        try:
            preset_manager = PresetManager(self)
            preset_manager.exec_()

        except Exception as e:
            self.log_message(f"打开预设管理器失败: {e}", "ERROR")
            QMessageBox.critical(self, "错误", f"打开预设管理器时发生错误:\n{str(e)}")

    def show_manual(self):
        """显示用户手册"""
        QMessageBox.information(self, "用户手册",
                               "用户手册功能正在开发中\n\n"
                               "基本使用步骤：\n"
                               "1. 加载A视频和B视频\n"
                               "2. 设置融合参数\n"
                               "3. 生成预览查看效果\n"
                               "4. 开始融合处理\n"
                               "5. 导出融合结果")

    def reset_project(self):
        """重置项目"""
        # 重置视频
        self.current_video_a_path = None
        self.current_video_b_path = None

        # 重置VideoPlayer组件
        if hasattr(self, 'video_a_player'):
            self.video_a_player.stop_video()
        if hasattr(self, 'video_b_player'):
            self.video_b_player.stop_video()

        # 清除预览
        self.clear_preview()

        # 重置融合引擎
        self.fusion_engine.reset()

        # 更新UI状态
        self.update_ui_state()

        self.status_label.setText("项目已重置")

    def reset_layout(self):
        """重置布局"""
        self.status_label.setText("布局已重置")
        self.log_message("布局已重置")

    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(self, "关于",
                         f"{self.config.get('application.name', '视频融合编辑器')}\n"
                         f"版本: {self.config.get('application.version', '1.0.0')}\n\n"
                         "桌面端视频编辑应用，实现A视频与B视频的多维度融合功能。\n\n"
                         "功能特点：\n"
                         "• 多种融合模式（插入、叠加、混合）\n"
                         "• 实时预览和参数调整\n"
                         "• 性能监控和优化\n"
                         "• 参数预设管理\n"
                         "• 拖拽文件支持")

    def closeEvent(self, event):
        """窗口关闭事件"""
        if self.is_fusion_running:
            reply = QMessageBox.question(
                self, "确认退出",
                "融合处理正在进行中，确定要退出吗？",
                QMessageBox.Yes | QMessageBox.No
            )

            if reply == QMessageBox.No:
                event.ignore()
                return

            # 停止融合处理
            self.stop_fusion()

        self.logger.info("应用程序退出")
        event.accept()
